import { boolean, varchar } from "drizzle-orm/pg-core";

import {
  createdAtCol,
  updatedAtCol,
  userIdFk,
  uuIdCol,
  uuIdFk,
} from "../../commonDbCols";
import { organizationUserTable } from "../core/organizationUser.table";
import { userTable } from "../core/user.table";
import { adSchema } from "../schemas";

export const linkedInUserTable = adSchema.table("linkedin_user", {
  organizationUserId: userIdFk("organization_user_id")
    .references(() => organizationUserTable.userId)
    .primaryKey(),
  refreshToken: varchar("refresh_token", { length: 1000 }).notNull(),
  isOrgDefaultToken: boolean("is_org_default_token").notNull().default(false),
  createdAt: createdAtCol,
  updatedAt: updatedAtCol,
});
