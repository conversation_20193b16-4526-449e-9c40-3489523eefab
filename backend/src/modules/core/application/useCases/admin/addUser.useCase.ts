import { ClerkClient, create<PERSON>ler<PERSON><PERSON><PERSON>, User } from "@clerk/backend";

import { db } from "@kalos/database";
import { linkedInOAuthHandlers } from "@kalos/database/handlers/linkedInOAuth";
import { organizationUserHandlers } from "@kalos/database/handlers/organizationUser";
import { userHandlers } from "@kalos/database/handlers/user";

import { organizationUserTable } from "../../../../../database/schemas/core/organizationUser.table";
import { LinkedInUserService } from "../../../../advertising/domain/services/linkedInUser.service";
import { UserService } from "../../../domain/services/user.service";
import { OrganizationRepository } from "../../../infrastructure/repositories/organization.repository";
import { UserRepository } from "../../../infrastructure/repositories/user.repository";
import { AddUserRequestDto } from "../../dtos/user/addUser.dto";
import { ITransaction } from "../../interfaces/infrastructure/services/transactionManager.service.interface";

export class AddUserUseCase {
  constructor(
    private readonly ctx: {
      userService: UserService;
      organizationRepository: OrganizationRepository;
      clerkClient: ClerkClient;
      linkedInUserService: LinkedInUserService;
    },
  ) {}

  async execute(input: AddUserRequestDto, tx?: ITransaction) {
    const organization = await this.ctx.organizationRepository.getOne(
      input.organizationId,
    );
    if (!organization) {
      throw new Error("Organization not found");
    }

    console.log(input.email);

    let clerkUser: User | undefined = undefined;

    try {
      clerkUser = await this.ctx.clerkClient.users.createUser({
        emailAddress: [input.email],
      });
    } catch (error) {
      const clerkUserList = await this.ctx.clerkClient.users.getUserList({
        emailAddress: [input.email],
      });
      if (clerkUserList.totalCount == 1) {
        clerkUser = clerkUserList.data[0];
      } else {
        throw new Error("Multiple users found with the same email");
      }
    }
    if (!clerkUser) {
      throw new Error("User not found");
    }

    try {
      const user = await this.ctx.userService.createUser(
        {
          userId: clerkUser.id,
          email: input.email,
          organizationId: input.organizationId,
        },
        tx,
      );
    } catch (error) {
      // Check if it's a duplicate key error
      if (
        error instanceof Error &&
        error.message.includes("duplicate key value violates unique constraint")
      ) {
        // Try to get the existing user to find which organization they belong to
        const existingUser = await this.ctx.userService.getUser(clerkUser.id);

        if (existingUser && existingUser.organizationId) {
          // Get the organization name
          const existingOrganization =
            await this.ctx.organizationRepository.getOne(
              existingUser.organizationId,
            );

          if (existingOrganization) {
            throw new Error(
              `User with email ${input.email} already exists in organization: ${existingOrganization.name}`,
            );
          } else {
            throw new Error(
              `User with email ${input.email} already exists in organization ID: ${existingUser.organizationId}`,
            );
          }
        } else {
          throw new Error(
            `User with email ${input.email} already exists in the system`,
          );
        }
      }
      // Re-throw any other error
      throw error;
    }

    await userHandlers.insert.one(clerkUser.id);
    await organizationUserHandlers.insert.one({
      organizationId: input.organizationId,
      userId: clerkUser.id,
    });
  }
}
