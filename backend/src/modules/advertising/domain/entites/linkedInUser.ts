import { z } from "zod";

import { linkedInAdAccountUrnSchema } from "../valueObjects/linkedInUrns/linkedInAdAccountUrn";

export const linkedInUserSchema = z.object({
  userId: z.string().min(1).max(33),
  linkedInAdAccounts: z.array(linkedInAdAccountUrnSchema),
  refreshToken: z.string().min(1).max(1000),
  isOrgDefaultToken: z.boolean().optional().default(false),
});

export type LinkedInUser = z.infer<typeof linkedInUserSchema>;
