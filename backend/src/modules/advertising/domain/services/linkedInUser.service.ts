import { ILinkedInUserRepository } from "../../application/interfaces/infrastructure/repositories/linkedInUser.Repository.interface";
import { LinkedInUser, linkedInUserSchema } from "../entites/linkedInUser";

export class LinkedInUserService {
  private linkedInUserRepository: ILinkedInUserRepository;

  constructor(ctx: { linkedInUserRepository: ILinkedInUserRepository }) {
    this.linkedInUserRepository = ctx.linkedInUserRepository;
  }
  buildLinkedInUser(input: LinkedInUser) {
    return linkedInUserSchema.parse(input);
  }

  async getLinkedInUser(userId: string) {
    console.log("getLinkedInUser", userId);
    const linkedInUser =
      await this.linkedInUserRepository.getLinkedInUser(userId);
    if (!linkedInUser) {
      return null;
    }
    const parsedLinkedInUser = this.buildLinkedInUser(linkedInUser);
    return parsedLinkedInUser;
  }

  async getOneByOrganizationId(organizationId: number) {
    const linkedInUser =
      await this.linkedInUserRepository.getOneByOrganizationId(organizationId);
    if (!linkedInUser) {
      return null;
    }

    const parsedLinkedInUser = this.buildLinkedInUser(linkedInUser);
    return parsedLinkedInUser;
  }

  async getOneByOrganizationIdDefault(organizationId: number) {
    const linkedInUser =
      await this.linkedInUserRepository.getOneByOrganizationIdDefault(
        organizationId,
      );
    if (!linkedInUser) {
      return null;
    }

    const parsedLinkedInUser = this.buildLinkedInUser(linkedInUser);
    return parsedLinkedInUser;
  }

  async createOne(input: LinkedInUser) {
    const linkedInUser =
      await this.linkedInUserRepository.createLinkedInUser(input);
    return linkedInUser;
  }

  async upsertOne(input: LinkedInUser) {
    const linkedInUser =
      await this.linkedInUserRepository.upsertLinkedInUser(input);
    return linkedInUser;
  }
}
