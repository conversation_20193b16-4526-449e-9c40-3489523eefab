import { z } from "zod";

import { organizationRoute } from "../../../../trpc/trpc";
import { getLinkedInUserDefaultResponseDto } from "../../application/dtos/controllerDtos/linkerInUserDtos/getLinkedInUserDefault.dto";
import { getDefaultLinkedInUserUseCase } from "../../application/useCase/linkedInUser/getDefaultLinkedInUser.useCase";
import { getLinkedInUserUseCase } from "../../application/useCase/linkedInUser/getLinkedInUserUseCase";
import { LinkedInUserService } from "../../domain/services/linkedInUser.service";
import { LinkedInUserRepository } from "../../infrastructure/repositories/linkedInUser.repository";

export const linkedInUserController = {
  getForUser: organizationRoute.query(async ({ ctx }) => {
    const linkedInUserService = new LinkedInUserService({
      linkedInUserRepository: new LinkedInUserRepository(),
    });
    return await getLinkedInUserUseCase({
      linkedInUserService: linkedInUserService,
      userId: ctx.userId,
    });
  }),

  getDefaultForOrganizationId: organizationRoute.query(async ({ ctx }) => {
    const linkedInUserService = new LinkedInUserService({
      linkedInUserRepository: new LinkedInUserRepository(),
    });
    return await getDefaultLinkedInUserUseCase({
      linkedInUserService: linkedInUserService,
      organizationId: ctx.organizationId,
    });
  }),

  upsert: organizationRoute
    .input(
      z.object({
        refreshToken: z.string(),
        isOrgDefaultToken: z.boolean().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const linkedInUserService = new LinkedInUserService({
        linkedInUserRepository: new LinkedInUserRepository(),
      });

      if (!input) {
        throw new Error("No input provided");
      }

      const isOrgDefaultToken = input.isOrgDefaultToken ?? false;

      return await linkedInUserService.upsertOne({
        userId: ctx.userId,
        refreshToken: input.refreshToken,
        linkedInAdAccounts: [],
        isOrgDefaultToken: isOrgDefaultToken,
      });
    }),
};
