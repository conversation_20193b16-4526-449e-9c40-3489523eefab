import { get } from "http";

import "../../../../../env";

import { Hono } from "hono";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/../../packages/linkedInApi/src/linkedInClient";
import { linkedInErrorHandler } from "@kalos/../../packages/linkedInApi/src/linkedInErrorHandler";

import { callerFactory } from "../../../../../trpc/router/trpcRouter";

const linkedinOAuthRoutes = new Hono();
https: linkedinOAuthRoutes.get("/", async (c) => {
  console.log("/api/oauth2/linkedin/callback Request received");
  const url = new URL(c.req.url);
  const code = url.searchParams.get("code");
  const userId = url.searchParams.get("state");

  interface TokenResponse {
    access_token: string;
    expires_in: number;
    refresh_token: string;
    error_description?: string;
  }

  if (!code) {
    return c.json(
      { error: "Authorization code or state missing" },
      { status: 400 },
    );
  }

  const clientId = process.env.LINKEDIN_CLIENT_ID;
  const clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
  console.log("clientid", clientId);
  if (!clientId || !clientSecret) {
    return c.json(
      {
        error:
          "/api/oauth2/linkedin/callback LinkedIn data not found in env  vars",
      },
      { status: 500 },
    );
  }

  const backendUrl = process.env.BACKEND_URL;
  console.log(`${backendUrl}/api/oauth2/linkedin/callback`);
  const params = new URLSearchParams({
    grant_type: "authorization_code",
    code: code,
    client_id: clientId,
    redirect_uri: `${backendUrl}/api/oauth2/linkedin/callback`,
    client_secret: clientSecret,
  });

  try {
    const tokenResponse = await fetch(
      "https://www.linkedin.com/oauth/v2/accessToken",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: params.toString(),
      },
    );

    const tokenData: TokenResponse =
      (await tokenResponse.json()) as TokenResponse;
    console.log(tokenResponse.headers.entries());

    if (!tokenResponse.ok) {
      const headers = Object.fromEntries(tokenResponse.headers.entries());

      console.error("/api/oauth2/linkedin/callback Token response was not OK:");

      // Notify OAuth error
      await linkedInErrorHandler.handleOAuthError(
        {
          error_description:
            tokenData.error_description || "Token exchange failed",
          liStatus: tokenResponse.status,
          liStatusText: tokenResponse.statusText,
          details: tokenData,
          headers,
        },
        {
          stage: "authorization_code_exchange",
          metadata: {
            httpStatus: tokenResponse.status,
            httpStatusText: tokenResponse.statusText,
          },
        },
        tokenResponse.status,
      );

      return c.json(
        {
          error: "LinkedIn API returned an error",
          details: tokenData,
          headers: headers,
          liStatus: tokenResponse.status,
          liStatusText: tokenResponse.statusText,
        },
        { status: 500 },
      );
    }

    const accessToken = tokenData.access_token;

    // const organizationId = clerkAuth?.orgId;

    if (!userId) {
      return c.json(
        {
          error:
            "/api/oauth2/linkedin/callback No user found in auth while authenticating",
        },
        { status: 401 },
      );
    }

    console.log("before caller", userId);

    const caller = await callerFactory({ userId: userId });

    const defaultLinkedInUser =
      await caller.v2.ads.linkedInUser.getDefaultForOrganizationId();

    const linkedInUser = await caller.v2.ads.linkedInUser.upsert({
      refreshToken: tokenData.refresh_token,
      isOrgDefaultToken: !defaultLinkedInUser,
    });

    if (!linkedInUser) {
      return c.json(
        {
          error: "oauth callback - Error upserting",
        },
        { status: 500 },
      );
    }
    console.log("/api/oauth2/linkedin/callback userId", userId);

    const organization = await caller.v2.core.user.getUser();

    if (!organization) {
      return c.json(
        {
          error:
            "/api/oauth2/linkedin/callback No organization found in org while authenticating",
        },
        { status: 500 },
      );
    }
    const organizationId = organization.organizationId;
    if (!organizationId) {
      return c.json(
        {
          error:
            "/api/oauth2/linkedin/callback No organization id found in org while authenticating",
        },
        { status: 401 },
      );
    }
    const client = await getLinkedInApiClientFromOrganizationId(
      Number(organizationId),
    );

    if (!client) {
      console.log(
        "/api/oauth2/linkedin/callback LinkedIn client could not be created after storing token",
      );

      // Notify client initialization error
      await linkedInErrorHandler.handleOAuthError(
        "LinkedIn client could not be created after storing token",
        {
          organizationId: Number(organizationId),
          stage: "client_initialization",
          metadata: {
            userId,
            organizationId: Number(organizationId),
          },
        },
        500,
      );
    }
    console.log("/api/oauth2/linkedin/callback client checked");

    const baseUrl = process.env.FRONTEND_URL;

    // No default user exists if client is onboarding
    // Go to performance screen if default linkedin user exists
    if (defaultLinkedInUser) {
      return c.redirect(baseUrl + "/advertising/performance");

      // else go to integrations screen - user is onboarding
    } else {
      return c.redirect(baseUrl + "/onboarding/integrations");
    }
    // Success! Redirect to the frontend
  } catch (error) {
    console.error("Error during LinkedIn OAuth flow:", error);

    // Notify OAuth error for exception cases
    await linkedInErrorHandler.handleOAuthError(
      error,
      {
        stage: "authorization_code_exchange",
        metadata: {
          errorType: "exception",
        },
      },
      500,
    );

    return c.json(
      { error: "Failed to get linkedin access token", details: String(error) },
      { status: 500 },
    );
  }
});

export { linkedinOAuthRoutes };
