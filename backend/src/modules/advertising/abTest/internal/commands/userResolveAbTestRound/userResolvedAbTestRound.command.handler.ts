import { err, ok, Result } from "neverthrow";

import { AbTestRoundDomain } from "../../domain/abTestRound.domain";
import { AbTestRoundDayDomain } from "../../domain/abTestRoundDay.domain";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { IAbTestRoundDayRepository } from "../../repositories/abTestRoundDay.repository.interface";
import { UserResolveAbTestRoundCommand } from "./userResolveAbTestRound.command.interface";

interface UserResolveAbTestRoundError {
  type: "AB_TEST_ROUND_NOT_FOUND" | "AB_TEST_ROUND_NOT_IN_PROGRESS";
}

export class UserResolveAbTestRoundCommandHandler {
  constructor(
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly abTestRoundDayRepository: IAbTestRoundDayRepository,
  ) {}
  async execute(
    command: UserResolveAbTestRoundCommand,
  ): Promise<Result<{ abTestId: string }, UserResolveAbTestRoundError>> {
    const abTestRound = await this.abTestRoundRepository.getOne(
      command.abTestRoundId,
      command.abTestType,
      command.tx,
    );

    if (!abTestRound) {
      return err({ type: "AB_TEST_ROUND_NOT_FOUND" });
    }

    if (abTestRound.status !== "IN_PROGRESS") {
      return err({ type: "AB_TEST_ROUND_NOT_IN_PROGRESS" });
    }

    const userResolvedRound = AbTestRoundDomain.userResolveAbTestRound({
      abTestRound,
      winner: command.winner,
    });

    if (userResolvedRound.isErr()) {
      return err(userResolvedRound.error);
    }

    const abTestRoundDays =
      await this.abTestRoundDayRepository.getAllForAbTestRound(
        abTestRound.id,
        command.abTestType,
        command.tx,
      );

    const inProgressAbTestRoundDays = abTestRoundDays.filter(
      (day) => day.status === "IN_PROGRESS",
    );

    await this.abTestRoundDayRepository.deleteMany(
      inProgressAbTestRoundDays.map((each) => each.id),
      command.abTestType,
      command.tx,
    );

    await this.abTestRoundRepository.update(
      userResolvedRound.value,
      command.abTestType,
      command.tx,
    );

    return ok({
      abTestId: abTestRound.abTestId,
    });
  }
}
