import { err, ok, Result } from "neverthrow";

import { Transaction } from "../../../../../../database/dbTransactionType";
import { createUuid } from "../../../../../core/utils/uuid";
import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestDomain } from "../../domain/abTest.domain";
import { AbTest } from "../../domain/abTest.entity";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { AdSegmentBestVariantRepository } from "../../repositories/adSegmentBestVariant.repository";
import { IAdSegmentBestVariantRepository } from "../../repositories/adSegmentBestVariant.repository.interface";
import { DataProviderRegistry } from "../../services/abTestDataProviders/dataProvider.registry";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { StartAbTestCommand } from "./startAbTest.command.interface";

type StartAbTestError = {
  type:
    | "STAGE_NOT_FOUND"
    | "STAGE_NOT_RUNNING"
    | "AB_TEST_ALREADY_EXISTS_FOR_STAGE"
    | "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS"
    | "NO_DATA_AVAILABLE_TO_SETUP_ROUNDS";
};

export class StartAbTestCommandHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly stageRepository: IStageRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly dataProvider: DataProviderService,
  ) {}

  async execute(
    command: StartAbTestCommand,
  ): Promise<
    Result<
      { abTest: AbTest; outcome: "AUTO_RESOLVED" | "IN_PROGRESS" },
      StartAbTestError
    >
  > {
    const stage = await this.stageRepository.getStage(command.stageId);
    if (!stage) {
      return err({ type: "STAGE_NOT_FOUND" });
    }
    if (stage.status !== "RUNNING") {
      return err({ type: "STAGE_NOT_RUNNING" });
    }

    const variants = await this.dataProvider.getVariantsToSetupRounds({
      adSegmentId: stage.adSegmentId,
      abTestType: command.type,
    });

    if (variants.isErr()) {
      return err({ type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS" });
    }
    if (variants.value.length == 0) {
      return err({ type: "NO_DATA_AVAILABLE_TO_SETUP_ROUNDS" });
    }
    const createAndStartResult = AbTestDomain.createAndStart({
      stage,
      type: command.type,
      variants: variants.value,
    });
    if (createAndStartResult.isErr()) {
      return err({ type: "AB_TEST_ALREADY_EXISTS_FOR_STAGE" });
    }

    await this.abTestRepository.createOne(
      createAndStartResult.value.abTest,
      command.tx,
    );

    if (createAndStartResult.value.outcome === "AUTO_RESOLVED") {
      const adSegmentBestVariant = new AdSegmentBestVariantRepository();
      await adSegmentBestVariant.upsertOne(
        {
          id: createUuid(),
          adSegmentId: stage.adSegmentId,
          type: command.type,
          variantId: createAndStartResult.value.abTest.winnerId!,
        },
        command.tx as Transaction,
      );
      return ok({
        abTest: createAndStartResult.value.abTest,
        outcome: "AUTO_RESOLVED",
      });
    }

    await this.abTestRoundRepository.createMany(
      createAndStartResult.value.rounds,
      createAndStartResult.value.abTest.type,
      command.tx,
    );

    return ok({
      abTest: createAndStartResult.value.abTest,
      outcome: "IN_PROGRESS",
    });
  }
}
