import { IAdCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/adCreative.repository.interface";
import { IAdSegmentValuePropRepository } from "../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IConversationSubjectCopyRepository } from "../../../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdAudienceRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { LinkedInSponsoredCreative } from "../../../../domain/entites/linkedInSponsoredCreative";
import { LinkedInService } from "../../../../infrastructure/services/linkedIn.service";
import { MapLinkedInStateInputToSponsoredCreativesService } from "../../../../linkedInStateOrchestrator/application/services/mapLinkedInStateInputToSponsoredCreatives.service";
import { AbTestRound } from "../../domain/abTestRound.entity";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { AbTestRoundDataDto } from "../../dtos/abTestRoundData.dto";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { IAbTestRoundDayRepository } from "../../repositories/abTestRoundDay.repository.interface";
import { IAbTestRoundDayMetricsRepository } from "../../repositories/abTestRoundDayMetrics.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { GetAbTestRoundDataCommand } from "./getAbTestRoundData.command.interface";

export class GetAbTestRoundDataQueryHandler {
  constructor(
    private readonly abTestRoundRepository: IAbTestRoundRepository,
    private readonly dataProvider: DataProviderService,
    private readonly linkedInService: LinkedInService,
    private readonly mapLinkedInStateInputToSponsoredCreativesService: MapLinkedInStateInputToSponsoredCreativesService,
    private readonly adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository,
    private readonly adSegmentValuePropRepository: IAdSegmentValuePropRepository,
    private readonly conversationSubjectCopyRepository: IConversationSubjectCopyRepository,
    private readonly adAudienceRepository: ILinkedInAdAudienceRepository,
    private readonly adCreateiveRepository: IAdCreativeRepository,
    private readonly abTestRoundDayMetricsRepository: IAbTestRoundDayMetricsRepository,
    private readonly abTestRoundDayRepository: IAbTestRoundDayRepository,
  ) {}
  async execute(
    command: GetAbTestRoundDataCommand,
  ): Promise<AbTestRoundDataDto | null> {
    const abTestRound = await this.abTestRoundRepository.getOne(
      command.abTestRoundId,
      command.type,
      command.tx,
    );
    if (!abTestRound) {
      return null;
    }
    console.log("abTestRound", abTestRound);

    const linkedInStateOrchestratorInput =
      await this.dataProvider.getLinkedInStateOrchestratorInput({
        adSegmentId: command.adSegmentId,
        abTestType: command.type,
        currentBestVariantId: abTestRound.currentBestId,
        contenderVariantId: abTestRound.contenderId,
      });

    if (linkedInStateOrchestratorInput.isErr()) {
      console.log(
        "Error getting linkedInStateOrchestratorInput",
        linkedInStateOrchestratorInput.error,
      );
      return null;
    }

    const abTestRoundDayMetrics =
      await this.abTestRoundDayMetricsRepository.getAllForAbTestRound(
        abTestRound.id,
        command.type,
        command.tx,
      );

    const abTestRoundDays =
      await this.abTestRoundDayRepository.getAllForAbTestRound(
        abTestRound.id,
        command.type,
        command.tx,
      );

    const lastMetrics = abTestRoundDayMetrics.sort(
      (a, b) => a.dayIndex - b.dayIndex,
    )[abTestRoundDayMetrics.length - 1]?.decisionType;

    if (
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_CONTENT"
    ) {
      const mappedSponsoredCreatives =
        await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredContent(
          linkedInStateOrchestratorInput.value,
        );

      const metrics = await this.linkedInService.getAnalyticsForCreatives({
        sponsoredCreativeUrns: mappedSponsoredCreatives.map(
          (creative) =>
            creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
        ),
        startDate: new Date("2024-01-01"),
        endDate: new Date("2025-01-01"),
        timeGranularity: "ALL",
      });

      const mappedSponsoredCreativesWithMetrics = mappedSponsoredCreatives.map(
        (creative) => {
          const metric = metrics.find(
            (metric) =>
              metric.sponsoredCreatieUrn ===
              creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
          );
          if (!metric) {
            throw new Error("Metrics not found");
          }
          return {
            ...creative,
            metrics: metric,
          };
        },
      );

      const currentBestMetrics = await mapForSponsoredContent(
        mappedSponsoredCreativesWithMetrics,
        command.type,
        abTestRound.currentBestId,
      );
      const contenderMetrics = await mapForSponsoredContent(
        mappedSponsoredCreativesWithMetrics,
        command.type,
        abTestRound.contenderId,
      );

      if (!currentBestMetrics || !contenderMetrics) {
        return null;
      }

      const currentBestVarientMap = await this.getVarientMapForSponsoredContent(
        {
          audienceId: currentBestMetrics.audienceId,
          valuePropId: currentBestMetrics.valuePropId,
          adProgramCreativeId: currentBestMetrics.adProgramCreativeId,
          socialPostBodyCopyType: currentBestMetrics.socialPostBodyCopyType,
          socialPostCallToActionType:
            currentBestMetrics.socialPostCallToActionType,
        },
      );

      const contenderVarientMap = await this.getVarientMapForSponsoredContent({
        audienceId: contenderMetrics.audienceId,
        valuePropId: contenderMetrics.valuePropId,
        adProgramCreativeId: contenderMetrics.adProgramCreativeId,
        socialPostBodyCopyType: contenderMetrics.socialPostBodyCopyType,
        socialPostCallToActionType: contenderMetrics.socialPostCallToActionType,
      });

      if (!currentBestVarientMap || !contenderVarientMap) {
        return null;
      }

      return {
        abTestId: abTestRound.abTestId,
        type: command.type,
        winner: abTestRound.winner,
        status: abTestRound.status,
        roundDays: abTestRoundDays,
        decsisionReason: lastMetrics ?? null,
        roundDayMetrics: abTestRoundDayMetrics,
        currentBest: {
          adId: currentBestMetrics.linkedInSponsoredCreative
            .linkedInSponseredCreativeUrn,
          adName: getAdNameForSponsoredContent(
            {
              audienceName: currentBestVarientMap.audienceType,
              valuePropName: currentBestVarientMap.valuePropType,
              adCreativeName: currentBestVarientMap.creativeType,
              socialPostBodyCopyType: currentBestMetrics.socialPostBodyCopyType,
              socialPostCallToActionType:
                currentBestMetrics.socialPostCallToActionType,
            },
            command.type,
          ),

          metrics: {
            clicks: currentBestMetrics.metrics.clicks,
            impressions: currentBestMetrics.metrics.impressions,
            cost: currentBestMetrics.metrics.costInUsd,
            leads: currentBestMetrics.metrics.oneClickLeads,
            actionClicks: currentBestMetrics.metrics.actionClicks,
            oneClickLeadFormOpens:
              currentBestMetrics.metrics.oneClickLeadFormOpens,
            landingPageClicks: currentBestMetrics.metrics.landingPageClicks,
            externalWebsiteConversions:
              currentBestMetrics.metrics.externalWebsiteConversions,
            videoViews: currentBestMetrics.metrics.videoViews,
            sends: currentBestMetrics.metrics.sends,
            opens: currentBestMetrics.metrics.opens,
            videoCompletions: currentBestMetrics.metrics.videoCompletions,
            videoFirstQuartileCompletions:
              currentBestMetrics.metrics.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              currentBestMetrics.metrics.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              currentBestMetrics.metrics.videoThirdQuartileCompletions,
            videoStarts: currentBestMetrics.metrics.videoStarts,
            totalEngagements: currentBestMetrics.metrics.totalEngagements,
            conversions: currentBestMetrics.metrics.externalWebsiteConversions,
          },
          varientId: abTestRound.currentBestId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_CONTENT",
            ...currentBestVarientMap,
          },
          sponsoredCreativeId: currentBestMetrics.linkedInSponsoredCreative.id,
        },
        contender: {
          adId: contenderMetrics.linkedInSponsoredCreative
            .linkedInSponseredCreativeUrn,
          adName: getAdNameForSponsoredContent(
            {
              audienceName: contenderVarientMap.audienceType,
              valuePropName: contenderVarientMap.valuePropType,
              adCreativeName: contenderVarientMap.creativeType,
              socialPostBodyCopyType: contenderMetrics.socialPostBodyCopyType,
              socialPostCallToActionType:
                contenderMetrics.socialPostCallToActionType,
            },
            command.type,
          ),
          metrics: {
            clicks: contenderMetrics.metrics.clicks,
            impressions: contenderMetrics.metrics.impressions,
            cost: contenderMetrics.metrics.costInUsd,
            leads: contenderMetrics.metrics.oneClickLeads,
            actionClicks: contenderMetrics.metrics.actionClicks,
            oneClickLeadFormOpens:
              contenderMetrics.metrics.oneClickLeadFormOpens,
            landingPageClicks: contenderMetrics.metrics.landingPageClicks,
            externalWebsiteConversions:
              contenderMetrics.metrics.externalWebsiteConversions,
            videoViews: contenderMetrics.metrics.videoViews,
            sends: contenderMetrics.metrics.sends,
            opens: contenderMetrics.metrics.opens,
            videoCompletions: contenderMetrics.metrics.videoCompletions,
            videoFirstQuartileCompletions:
              contenderMetrics.metrics.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              contenderMetrics.metrics.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              contenderMetrics.metrics.videoThirdQuartileCompletions,
            videoStarts: contenderMetrics.metrics.videoStarts,
            totalEngagements: contenderMetrics.metrics.totalEngagements,
            conversions: contenderMetrics.metrics.externalWebsiteConversions,
          },
          varientId: abTestRound.contenderId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_CONTENT",
            ...contenderVarientMap,
          },
          sponsoredCreativeId: contenderMetrics.linkedInSponsoredCreative.id,
        },
      };
    } else if (
      linkedInStateOrchestratorInput.value.adFormatType == "SPONSORED_INMAIL"
    ) {
      const mappedSponsoredCreatives =
        await this.mapLinkedInStateInputToSponsoredCreativesService.setupLinkedInStateForSponsoredInmail(
          linkedInStateOrchestratorInput.value,
        );

      const metrics = await this.linkedInService.getAnalyticsForCreatives({
        sponsoredCreativeUrns: mappedSponsoredCreatives.map(
          (creative) =>
            creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
        ),
        startDate: new Date("2024-01-01"),
        endDate: new Date("2025-01-01"),
        timeGranularity: "ALL",
      });

      const mappedSponsoredCreativesWithMetrics = mappedSponsoredCreatives.map(
        (creative) => {
          const metric = metrics.find(
            (metric) =>
              metric.sponsoredCreatieUrn ===
              creative.linkedInSponsoredCreative.linkedInSponseredCreativeUrn,
          );
          if (!metric) {
            throw new Error("Metrics not found");
          }
          return {
            ...creative,
            metrics: metric,
          };
        },
      );

      const currentBestMetrics = await mapForSponsoredInmail(
        mappedSponsoredCreativesWithMetrics,
        command.type,
        abTestRound.currentBestId,
      );
      const contenderMetrics = await mapForSponsoredInmail(
        mappedSponsoredCreativesWithMetrics,
        command.type,
        abTestRound.contenderId,
      );

      if (!currentBestMetrics || !contenderMetrics) {
        return null;
      }

      const currentBestVarientMap = await this.getVarientMapForSponsoredInmail({
        audienceId: currentBestMetrics.audienceId,
        valuePropId: currentBestMetrics.valuePropId,
        conversationSubjectCopyType:
          currentBestMetrics.conversationSubjectCopyType,
        conversationMessageCopyType:
          currentBestMetrics.conversationMessageCopyType,
        conversationCallToActionType:
          currentBestMetrics.conversationCallToActionCopyType,
      });

      const contenderVarientMap = await this.getVarientMapForSponsoredInmail({
        audienceId: contenderMetrics.audienceId,
        valuePropId: contenderMetrics.valuePropId,
        conversationSubjectCopyType:
          contenderMetrics.conversationSubjectCopyType,
        conversationMessageCopyType:
          contenderMetrics.conversationMessageCopyType,
        conversationCallToActionType:
          contenderMetrics.conversationCallToActionCopyType,
      });

      if (!currentBestVarientMap || !contenderVarientMap) {
        return null;
      }

      return {
        abTestId: abTestRound.abTestId,
        type: command.type,
        winner: abTestRound.winner,
        status: abTestRound.status,
        roundDayMetrics: abTestRoundDayMetrics,
        roundDays: abTestRoundDays,
        decsisionReason: lastMetrics ?? null,
        currentBest: {
          adId: currentBestMetrics.linkedInSponsoredCreative
            .linkedInSponseredCreativeUrn,
          adName: getAdNameForSponsoredInmail(
            {
              audienceName: currentBestVarientMap.audienceType,
              valuePropName: currentBestVarientMap.valuePropType,
              conversationSubjectCopyType:
                currentBestMetrics.conversationSubjectCopyType,
              conversationMessageCopyType:
                currentBestMetrics.conversationMessageCopyType,
              conversationCallToActionCopyType:
                currentBestMetrics.conversationCallToActionCopyType,
            },
            command.type,
          ),
          metrics: {
            clicks: currentBestMetrics.metrics.clicks,
            impressions: currentBestMetrics.metrics.impressions,
            cost: currentBestMetrics.metrics.costInUsd,
            leads: currentBestMetrics.metrics.oneClickLeads,
            actionClicks: currentBestMetrics.metrics.actionClicks,
            oneClickLeadFormOpens:
              currentBestMetrics.metrics.oneClickLeadFormOpens,
            landingPageClicks: currentBestMetrics.metrics.landingPageClicks,
            externalWebsiteConversions:
              currentBestMetrics.metrics.externalWebsiteConversions,
            videoViews: currentBestMetrics.metrics.videoViews,
            sends: currentBestMetrics.metrics.sends,
            opens: currentBestMetrics.metrics.opens,
            videoCompletions: currentBestMetrics.metrics.videoCompletions,
            videoFirstQuartileCompletions:
              currentBestMetrics.metrics.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              currentBestMetrics.metrics.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              currentBestMetrics.metrics.videoThirdQuartileCompletions,
            videoStarts: currentBestMetrics.metrics.videoStarts,
            totalEngagements: currentBestMetrics.metrics.totalEngagements,
            conversions: currentBestMetrics.metrics.externalWebsiteConversions,
          },
          varientId: abTestRound.currentBestId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_INMAIL",
            ...currentBestVarientMap,
          },
          sponsoredCreativeId: currentBestMetrics.linkedInSponsoredCreative.id,
        },
        contender: {
          adId: contenderMetrics.linkedInSponsoredCreative
            .linkedInSponseredCreativeUrn,
          adName: getAdNameForSponsoredInmail(
            {
              audienceName: contenderVarientMap.audienceType,
              valuePropName: contenderVarientMap.valuePropType,
              conversationSubjectCopyType:
                contenderMetrics.conversationSubjectCopyType,
              conversationMessageCopyType:
                contenderMetrics.conversationMessageCopyType,
              conversationCallToActionCopyType:
                contenderMetrics.conversationCallToActionCopyType,
            },
            command.type,
          ),
          metrics: {
            clicks: contenderMetrics.metrics.clicks,
            impressions: contenderMetrics.metrics.impressions,
            cost: contenderMetrics.metrics.costInUsd,
            leads: contenderMetrics.metrics.oneClickLeads,
            actionClicks: contenderMetrics.metrics.actionClicks,
            oneClickLeadFormOpens:
              contenderMetrics.metrics.oneClickLeadFormOpens,
            landingPageClicks: contenderMetrics.metrics.landingPageClicks,
            externalWebsiteConversions:
              contenderMetrics.metrics.externalWebsiteConversions,
            videoViews: contenderMetrics.metrics.videoViews,
            sends: contenderMetrics.metrics.sends,
            opens: contenderMetrics.metrics.opens,
            videoCompletions: contenderMetrics.metrics.videoCompletions,
            videoFirstQuartileCompletions:
              contenderMetrics.metrics.videoFirstQuartileCompletions,
            videoMidpointCompletions:
              contenderMetrics.metrics.videoMidpointCompletions,
            videoThirdQuartileCompletions:
              contenderMetrics.metrics.videoThirdQuartileCompletions,
            videoStarts: contenderMetrics.metrics.videoStarts,
            totalEngagements: contenderMetrics.metrics.totalEngagements,
            conversions: contenderMetrics.metrics.externalWebsiteConversions,
          },
          varientId: abTestRound.contenderId,
          varientsUsedInSponsoredCreative: {
            adFormatType: "SPONSORED_INMAIL",
            ...contenderVarientMap,
          },
          sponsoredCreativeId: contenderMetrics.linkedInSponsoredCreative.id,
        },
      };
    } else {
      throw new Error("Not implemented");
    }
  }

  async getVarientMapForSponsoredContent(input: {
    audienceId: string;
    valuePropId: string;
    adProgramCreativeId: string;
    socialPostBodyCopyType: string;
    socialPostCallToActionType: string;
  }) {
    const [audience, valueProp, adProgramCreative] = await Promise.all([
      this.adAudienceRepository.getOne(input.audienceId),
      this.adSegmentValuePropRepository.getOne(input.valuePropId, "ACTIVE"),
      this.adProgramCreativeRepository.getOne(input.adProgramCreativeId),
    ]);
    if (!audience || !valueProp || !adProgramCreative) {
      return null;
    }

    const adCreative = await this.adCreateiveRepository.getOneById(
      adProgramCreative.adCreativeId,
    );
    if (!adCreative) {
      return null;
    }

    return {
      audienceType: audience.audienceTargetCriteria.include.and
        .map((each) => each.or.map((each) => each.facetName))
        .join(", "),
      valuePropType: valueProp.valueProp,
      creativeType: adCreative.fileName,
      socialPostBodyCopyType: input.socialPostBodyCopyType,
      socialPostCallToActionType: input.socialPostCallToActionType,
    };
  }

  async getVarientMapForSponsoredInmail(input: {
    audienceId: string;
    valuePropId: string;
    conversationSubjectCopyType: string;
    conversationMessageCopyType: string;
    conversationCallToActionType: string;
  }) {
    const [audience, valueProp] = await Promise.all([
      this.adAudienceRepository.getOne(input.audienceId),
      this.adSegmentValuePropRepository.getOne(input.valuePropId, "ACTIVE"),
    ]);
    if (!audience || !valueProp) {
      return null;
    }

    return {
      audienceType: audience.audienceTargetCriteria.include.and
        .map((each) => each.or.map((each) => each.facetName))
        .join(", "),
      valuePropType: valueProp.valueProp,
      conversationSubjectCopyType: input.conversationSubjectCopyType,
      conversationMessageCopyType: input.conversationMessageCopyType,
      conversationCallToActionType: input.conversationCallToActionType,
    };
  }
}

async function mapForSponsoredContent(
  input: {
    linkedInSponsoredCreative: LinkedInSponsoredCreative;
    audienceId: string;
    valuePropId: string;
    adProgramCreativeId: string;
    socialPostBodyCopyType: string;
    socialPostCallToActionType: string;
    metrics: {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      totalEngagements: number;
    };
  }[],
  type: AbTestType,
  target: string,
) {
  if (type == "audience") {
    return input.find((each) => each.audienceId == target);
  } else if (type == "valueProp") {
    return input.find((each) => each.valuePropId == target);
  } else if (type == "creative") {
    return input.find((each) => each.adProgramCreativeId == target);
  } else if (type == "socialPostBodyCopy") {
    return input.find((each) => each.socialPostBodyCopyType == target);
  } else if (type == "socialPostCallToAction") {
    return input.find((each) => each.socialPostCallToActionType == target);
  } else {
    return null;
  }
}

async function mapForSponsoredInmail(
  input: {
    linkedInSponsoredCreative: LinkedInSponsoredCreative;
    audienceId: string;
    valuePropId: string;
    conversationSubjectCopyType: string;
    conversationMessageCopyType: string;
    conversationCallToActionCopyType: string;
    metrics: {
      sponsoredCreatieUrn: string;
      clicks: number;
      impressions: number;
      costInUsd: number;
      oneClickLeads: number;
      actionClicks: number;
      oneClickLeadFormOpens: number;
      landingPageClicks: number;
      externalWebsiteConversions: number;
      videoViews: number;
      sends: number;
      opens: number;
      videoCompletions: number;
      videoFirstQuartileCompletions: number;
      videoMidpointCompletions: number;
      videoThirdQuartileCompletions: number;
      videoStarts: number;
      totalEngagements: number;
    };
  }[],
  type: AbTestType,
  target: string,
) {
  if (type == "audience") {
    return input.find((each) => each.audienceId == target);
  } else if (type == "valueProp") {
    return input.find((each) => each.valuePropId == target);
  } else if (type == "conversationSubject") {
    return input.find((each) => each.conversationSubjectCopyType == target);
  } else if (type == "conversationMessageCopy") {
    return input.find((each) => each.conversationMessageCopyType == target);
  } else if (type == "conversationCallToAction") {
    return input.find(
      (each) => each.conversationCallToActionCopyType == target,
    );
  } else {
    return null;
  }
}

function getAdNameForSponsoredContent(
  input: {
    audienceName: string;
    valuePropName: string;
    adCreativeName: string;
    socialPostBodyCopyType: string;
    socialPostCallToActionType: string;
  },
  type: AbTestType,
) {
  if (type == "audience") {
    return input.audienceName;
  } else if (type == "valueProp") {
    return input.valuePropName;
  } else if (type == "creative") {
    return input.adCreativeName;
  } else if (type == "socialPostBodyCopy") {
    return input.socialPostBodyCopyType;
  } else if (type == "socialPostCallToAction") {
    return input.socialPostCallToActionType;
  } else {
    throw new Error("Invalid type");
  }
}

function getAdNameForSponsoredInmail(
  input: {
    audienceName: string;
    valuePropName: string;
    conversationSubjectCopyType: string;
    conversationMessageCopyType: string;
    conversationCallToActionCopyType: string;
  },
  type: AbTestType,
) {
  if (type == "audience") {
    return input.audienceName;
  } else if (type == "valueProp") {
    return input.valuePropName;
  } else if (type == "conversationSubject") {
    return input.conversationSubjectCopyType;
  } else if (type == "conversationMessageCopy") {
    return input.conversationMessageCopyType;
  } else if (type == "conversationCallToAction") {
    return input.conversationCallToActionCopyType;
  } else {
    throw new Error("Invalid type");
  }
}
