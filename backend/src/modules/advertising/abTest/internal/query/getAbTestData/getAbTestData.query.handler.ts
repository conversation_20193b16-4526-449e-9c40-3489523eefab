import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../../../application/interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { IStageRepository } from "../../../../application/interfaces/infrastructure/repositories/stage.repository.interface";
import { Stage } from "../../../../domain/entites/stage";
import { AbTest } from "../../domain/abTest.entity";
import { AbTestRound } from "../../domain/abTestRound.entity";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { AbTestDataDto } from "../../dtos/abTestData.dto";
import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { IAbTestRoundRepository } from "../../repositories/abTestRound.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { GetAbTestDataCommand } from "./getAbTestData.query.interface";

export class GetAbTestDataQueryHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly abTestRoundRepository: IAbTestRoundRepository,
  ) {}
  async execute(command: GetAbTestDataCommand): Promise<AbTestDataDto | null> {
    const abTest = await this.abTestRepository.getOne(
      command.stage.id,
      command.type,
    );
    if (!abTest) {
      return null;
    }

    return this.getAbTestDataForCurrentRunningAbTest(
      abTest,
      command.type,
      command.tx,
    );
  }

  private async getAbTestDataForCurrentRunningAbTest(
    abTest: AbTest,
    type: AbTestType,
    tx: ITransaction,
  ): Promise<AbTestDataDto | null> {
    const [abTestRounds] = await Promise.all([
      this.abTestRoundRepository.getAllForAbTest(abTest.stageId, type, tx),
    ]);

    const currentRound = abTestRounds.find(
      (round) => round.status === "IN_PROGRESS",
    );

    const pastRounds = abTestRounds.filter(
      (round) =>
        round.status == "COMPLETED" ||
        round.status === "AUTO_RESOLVED" ||
        round.status === "USER_RESOLVED" ||
        round.status === "FAILED" ||
        round.status === "CANCELLED",
    );

    const upcomingRounds = abTestRounds.filter(
      (round) => round.status === "NOT_STARTED",
    );

    const res: AbTestDataDto = {
      abTestId: abTest.stageId,
      status: abTest.status,
      type: abTest.type,
      currentRound: currentRound
        ? {
            id: currentRound.id,
            roundIndex: currentRound.roundIndex,
            currentBestId: currentRound.currentBestId,
            contenderId: currentRound.contenderId,
          }
        : null,
      pastRounds: pastRounds.map((round) => ({
        id: round.id,
        roundIndex: round.roundIndex,
        currentBestId: round.currentBestId,
        contenderId: round.contenderId,
        winner: round.winner == "CONTENDER" ? "CONTENDER" : "CURRENT_BEST",
      })),
      upcomingRounds: upcomingRounds.map((round) => ({
        id: round.id,
        roundIndex: round.roundIndex,
        currentBestId: round.currentBestId,
        contenderId: round.contenderId,
      })),
    };
    return res;
  }
}
