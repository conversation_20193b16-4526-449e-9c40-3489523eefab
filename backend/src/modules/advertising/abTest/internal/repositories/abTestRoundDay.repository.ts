import { eq, inArray } from "drizzle-orm";

import { Transaction } from "../../../../../database/db";
import { AbTestRoundDay } from "../domain/abTestRoundDay.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRoundDayRepository } from "./abTestRoundDay.repository.interface";
import { abTestTableRegistry } from "./abTestTables.registry";

export class AbTestRoundDayRepository implements IAbTestRoundDayRepository {
  async createOne(
    abTestRoundDay: AbTestRoundDay,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDay> {
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;
    await tx.insert(abTestRoundDayTable).values(abTestRoundDay);
    return abTestRoundDay;
  }

  async getAllForAbTestRound(
    abTestRoundId: string,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDay[]> {
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;
    const res = await tx
      .select()
      .from(abTestRoundDayTable)
      .where(eq(abTestRoundDayTable.abTestRoundId, abTestRoundId));
    const abTestRoundDays: AbTestRoundDay[] = [];

    for (const r of res) {
      if (
        r.status === "COMPLETED" ||
        r.status === "AUTO_RESOLVED" ||
        r.status === "USER_RESOLVED"
      ) {
        if (r.winner == null) {
          throw new Error("Winner is null when round day is completed");
        }
        if (r.currentBestResult == null || r.contenderResult == null) {
          throw new Error(
            "Current best result or contender result is null when round day is completed",
          );
        }
        abTestRoundDays.push(
          AbTestRoundDay({
            id: r.id,
            abTestRoundId: r.abTestRoundId,
            dayIndex: r.dayIndex,
            deploymentConfigId: r.deploymentConfigId,
            status: r.status,
            winner: r.winner,
            currentBestResult: r.currentBestResult,
            contenderResult: r.contenderResult,
          }),
        );
      } else {
        abTestRoundDays.push(
          AbTestRoundDay({
            id: r.id,
            abTestRoundId: r.abTestRoundId,
            dayIndex: r.dayIndex,
            deploymentConfigId: r.deploymentConfigId,
            status: r.status,
            winner: null,
            currentBestResult: null,
            contenderResult: null,
          }),
        );
      }
    }
    return abTestRoundDays;
  }

  async getOne(
    abTestRoundDayId: string,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDay | null> {
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;
    const res = await tx
      .select()
      .from(abTestRoundDayTable)
      .where(eq(abTestRoundDayTable.id, abTestRoundDayId));
    if (!res[0]) {
      return null;
    }
    if (
      res[0].status == "COMPLETED" ||
      res[0].status == "AUTO_RESOLVED" ||
      res[0].status == "USER_RESOLVED"
    ) {
      if (res[0].winner == null) {
        throw new Error("Winner is null when round day is completed");
      }
      if (res[0].currentBestResult == null || res[0].contenderResult == null) {
        throw new Error(
          "Current best result or contender result is null when round day is completed",
        );
      }
      return AbTestRoundDay({
        id: res[0].id,
        abTestRoundId: res[0].abTestRoundId,
        dayIndex: res[0].dayIndex,
        deploymentConfigId: res[0].deploymentConfigId,
        status: res[0].status,
        winner: res[0].winner,
        currentBestResult: res[0].currentBestResult,
        contenderResult: res[0].contenderResult,
      });
    }
    return AbTestRoundDay({
      id: res[0].id,
      abTestRoundId: res[0].abTestRoundId,
      dayIndex: res[0].dayIndex,
      deploymentConfigId: res[0].deploymentConfigId,
      status: res[0].status,
      winner: null,
      currentBestResult: null,
      contenderResult: null,
    });
  }

  async updateOne(
    abTestRoundDay: AbTestRoundDay,
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<AbTestRoundDay> {
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;

    await tx
      .update(abTestRoundDayTable)
      .set(abTestRoundDay)
      .where(eq(abTestRoundDayTable.id, abTestRoundDay.id));
    return abTestRoundDay;
  }

  async deleteMany(
    abTestRoundDayIds: string[],
    abTestType: AbTestType,
    tx: Transaction,
  ): Promise<void> {
    const abTestRoundDayTable =
      abTestTableRegistry[abTestType].abTestRoundDayTable;

    if (abTestRoundDayIds.length === 0) {
      return;
    }
    await tx
      .delete(abTestRoundDayTable)
      .where(inArray(abTestRoundDayTable.id, abTestRoundDayIds));
  }
}
