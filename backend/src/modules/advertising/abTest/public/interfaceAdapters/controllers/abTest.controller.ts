import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { getLinkedInApiClientFromOrganizationId } from "@kalos/linkedin-api";

import { organizationRoute } from "../../../../../../trpc/trpc";
import { TransactionManagerService } from "../../../../../core/infrastructure/services/transcationManager.service";
import { InngestJobTriggerPublisher } from "../../../../../shared/inngestJobTriggerPublisher";
import { AdCreativeRepository } from "../../../../infrastructure/repositories/adCreative.repository";
import { AdSegmentValuePropRepository } from "../../../../infrastructure/repositories/adSegmentValueProp.repository";
import { ConversationCallToActionCopyRepository } from "../../../../infrastructure/repositories/conversationCallToActionCopy.repository";
import { ConversationSubjectCopyRepository } from "../../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdAccountRepository } from "../../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdProgramCreativeRepository } from "../../../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { LinkedInAdSegmentRepository } from "../../../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../../../infrastructure/repositories/linkedInCampaign.repository";
import { LinkedInPostRepository } from "../../../../infrastructure/repositories/linkedInPost.repository";
import { LinkedInSponsoredCreativeRepository } from "../../../../infrastructure/repositories/linkedInSponsoredCreative.repository";
import { StageRepository } from "../../../../infrastructure/repositories/stage.repository";
import { LinkedInService } from "../../../../infrastructure/services/linkedIn.service";
import { MapLinkedInStateInputToSponsoredCreativesService } from "../../../../linkedInStateOrchestrator/application/services/mapLinkedInStateInputToSponsoredCreatives.service";
import { LinkedInStateConfigRepository } from "../../../../linkedInStateOrchestrator/repository/linkedInStateConfig.repository";
import { advertisingInngestClient } from "../../../../utils/advertisingInngestClient";
import { UserResolveAbTestRoundCommandHandler } from "../../../internal/commands/userResolveAbTestRound/userResolvedAbTestRound.command.handler";
import {
  AbTestType,
  abTestTypeSchema,
} from "../../../internal/domain/abTestType.valueObject";
import { endAbTestJobTrigger } from "../../../internal/jobTriggers/endAbTest.jobTrigger";
import { startNextAbTestRoundJobTrigger } from "../../../internal/jobTriggers/startNextAbTestRound.jobTrigger";
import { GetAbTestDataQueryHandler } from "../../../internal/query/getAbTestData/getAbTestData.query.handler";
import { GetAbTestRoundDataQueryHandler } from "../../../internal/query/getAbTestRoundData/getAbTestRoundData.command.handler";
import { GetAbTestsQueryHandler } from "../../../internal/query/getAbTests/getAbTests.query.handler";
import { AbTestRepository } from "../../../internal/repositories/abTest.repository";
import { AbTestRoundRepository } from "../../../internal/repositories/abTestRound.repository";
import { AbTestRoundDayRepository } from "../../../internal/repositories/abTestRoundDay.repository";
import { AbTestRoundDayMetricsRepository } from "../../../internal/repositories/abTestRoundDayMetrics.repository";
import { DataProviderService } from "../../../internal/services/abTestDataProviders/dataProvider.service";

export const newAbTestController = {
  getAbTestData: organizationRoute
    .input(
      z.object({
        abTestId: z.string(),
        adSegmentId: z.string(),
        type: abTestTypeSchema,
      }),
    )
    .query(async ({ input, ctx }) => {
      const adAccountRepository = new LinkedInAdAccountRepository();
      const adAccount = await adAccountRepository.getForOrganization(
        ctx.organizationId,
      );
      if (!adAccount || adAccount.length === 0) {
        throw new Error("No ad account found for organization");
      }
      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }

      const getAbTestData = new GetAbTestDataQueryHandler(
        new AbTestRepository(),
        new AbTestRoundRepository(),
      );

      const transactionManager = new TransactionManagerService();

      const stage = await new StageRepository().getStage(input.abTestId);
      if (!stage) {
        throw new Error("Stage not found");
      }

      let abTestType: AbTestType | null = null;
      if (stage.stageType === "audienceTest") {
        abTestType = "audience";
      } else if (stage.stageType === "valuePropTest") {
        abTestType = "valueProp";
      } else if (stage.stageType === "creativeTest") {
        abTestType = "creative";
      } else if (stage.stageType === "conversationSubjectTest") {
        abTestType = "conversationSubject";
      } else if (stage.stageType === "conversationMessageCopyTest") {
        abTestType = "conversationMessageCopy";
      } else if (stage.stageType === "conversationCallToActionTest") {
        abTestType = "conversationCallToAction";
      } else if (stage.stageType === "socialPostBodyCopyTest") {
        abTestType = "socialPostBodyCopy";
      }

      if (!abTestType) {
        throw new Error("Invalid stage type");
      }

      const res = await transactionManager.startTransaction(async (tx) => {
        const res = await getAbTestData.execute({
          stage: stage,
          type: abTestType,
          tx,
        });
        return res;
      });

      return res;
    }),

  getAbTests: organizationRoute
    .input(
      z.object({
        adSegmentId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const getAbTests = new GetAbTestsQueryHandler(new AbTestRepository());

      const transactionManager = new TransactionManagerService();

      const res = await transactionManager.startTransaction(async (tx) => {
        const res = await getAbTests.execute({
          adSegmentId: input.adSegmentId,
        });
        return res;
      });

      return res;
    }),

  getAbTestRoundData: organizationRoute
    .input(
      z.object({
        abTestRoundId: z.string(),
        adSegmentId: z.string(),
        type: abTestTypeSchema,
      }),
    )
    .query(async ({ input, ctx }) => {
      const adAccountRepository = new LinkedInAdAccountRepository();
      const adAccount = await adAccountRepository.getForOrganization(
        ctx.organizationId,
      );
      if (!adAccount || adAccount.length === 0) {
        throw new Error("No ad account found for organization");
      }

      const linkedInClient = await getLinkedInApiClientFromOrganizationId(
        ctx.organizationId,
      );
      if (!linkedInClient) {
        throw new Error("LinkedIn client not found");
      }

      const getAbTestData = new GetAbTestRoundDataQueryHandler(
        new AbTestRoundRepository(),
        new DataProviderService({
          adSegmentRepository: new LinkedInAdSegmentRepository(),
          adProgramRepository: new LinkedInAdProgramRepository(),
        }),
        new LinkedInService(linkedInClient),
        new MapLinkedInStateInputToSponsoredCreativesService({
          linkedInPostRepository: new LinkedInPostRepository(),
          linkedInSponsoredCreativeRepository:
            new LinkedInSponsoredCreativeRepository(),
          linkedInCampaignRepository: new LinkedInCampaignRepository(),
          linkedInConversationCallToActionRepository:
            new ConversationCallToActionCopyRepository(),
          linkedInStateConfigRepository: new LinkedInStateConfigRepository(),
          linkedInService: new LinkedInService(linkedInClient),
          linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
          adSegmentRepository: new LinkedInAdSegmentRepository(),
          adProgramRepository: new LinkedInAdProgramRepository(),
        }),
        new LinkedInAdProgramCreativeRepository(),
        new AdSegmentValuePropRepository(),
        new ConversationSubjectCopyRepository(),
        new LinkedInAdAudienceRepository(),
        new AdCreativeRepository(),
        new AbTestRoundDayMetricsRepository(),
        new AbTestRoundDayRepository(),
      );

      const transactionManager = new TransactionManagerService();

      const res = await transactionManager.startTransaction(async (tx) => {
        const res = await getAbTestData.execute({
          ...input,
          tx,
        });
        return res;
      });

      return res;
    }),

  userResolveAbTestRound: organizationRoute
    .input(
      z.object({
        abTestRoundId: z.string(),
        abTestType: abTestTypeSchema,
        winner: z.enum(["CURRENT_BEST", "CONTENDER"]),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userResolveAbTestRound = new UserResolveAbTestRoundCommandHandler(
        new AbTestRoundRepository(),
        new AbTestRoundDayRepository(),
      );
      const transactionManager = new TransactionManagerService();

      await transactionManager.startTransaction(async (tx) => {
        const res = await userResolveAbTestRound.execute({
          abTestRoundId: input.abTestRoundId,
          abTestType: input.abTestType,
          winner: input.winner,
          tx,
        });
        if (res.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: res.error.type,
          });
        }

        const abTestRoundRepository = new AbTestRoundRepository();

        const notStartedRounds = (
          await abTestRoundRepository.getAllForAbTest(
            res.value.abTestId,
            input.abTestType,
            tx,
          )
        ).filter((round) => round.status === "NOT_STARTED");

        const jobTriggerPublisher = InngestJobTriggerPublisher(
          advertisingInngestClient,
        );

        if (notStartedRounds.length > 0) {
          jobTriggerPublisher.publish(
            startNextAbTestRoundJobTrigger.build({
              abTestType: input.abTestType,
              abTestId: res.value.abTestId,
            }),
          );
        } else {
          await jobTriggerPublisher.publish(
            endAbTestJobTrigger.build({
              abTestId: res.value.abTestId,
              abTestType: input.abTestType,
            }),
          );
        }
      });
    }),
};
