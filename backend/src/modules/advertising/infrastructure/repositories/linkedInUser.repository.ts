import { and, eq } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import { linkedInUserTable } from "../../../../database/schemas/advertising/linkedInUser.table";
import { linkedInUserToAdAccountTable } from "../../../../database/schemas/advertising/linkedInUserToAdAccount.table";
import { organizationUserTable } from "../../../../database/schemas/core/organizationUser.table";
import { ILinkedInUserRepository } from "../../application/interfaces/infrastructure/repositories/linkedInUser.Repository.interface";
import { LinkedInUser } from "../../domain/entites/linkedInUser";

export class LinkedInUserRepository implements ILinkedInUserRepository {
  async getLinkedInUser(
    userId: string,
    tx?: Transaction,
  ): Promise<LinkedInUser | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInUserTable)
      .where(eq(linkedInUserTable.organizationUserId, userId))
      .leftJoin(
        linkedInUserToAdAccountTable,
        eq(
          linkedInUserTable.organizationUserId,
          linkedInUserToAdAccountTable.linkedInUserId,
        ),
      );
    if (!res[0]) {
      return null;
    }

    const accountIds: string[] = [];

    for (const adAccount of res) {
      if (adAccount.linkedin_user_to_ad_account) {
        accountIds.push(
          adAccount.linkedin_user_to_ad_account.linkedInAdAccountId,
        );
      }
    }

    return {
      userId: res[0].linkedin_user.organizationUserId,
      refreshToken: res[0].linkedin_user.refreshToken,
      linkedInAdAccounts: accountIds,
      isOrgDefaultToken: res[0].linkedin_user.isOrgDefaultToken,
    };
  }

  async getOneByOrganizationId(organizationId: number, tx?: Transaction) {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInUserTable)
      .innerJoin(
        organizationUserTable,
        eq(linkedInUserTable.organizationUserId, organizationUserTable.userId),
      )
      .where(eq(organizationUserTable.organizationId, organizationId));
    if (!res[0]) {
      return null;
    }
    return {
      userId: res[0].linkedin_user.organizationUserId,
      refreshToken: res[0].linkedin_user.refreshToken,
      linkedInAdAccounts: [],
      isOrgDefaultToken: res[0].linkedin_user.isOrgDefaultToken,
    };
  }

  async getOneByOrganizationIdDefault(
    organizationId: number,
    tx?: Transaction,
  ) {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(linkedInUserTable)
      .innerJoin(
        organizationUserTable,
        eq(linkedInUserTable.organizationUserId, organizationUserTable.userId),
      )
      .where(
        and(
          eq(organizationUserTable.organizationId, organizationId),
          eq(linkedInUserTable.isOrgDefaultToken, true),
        ),
      );
    if (!res[0]) {
      return null;
    }
    return {
      userId: res[0].linkedin_user.organizationUserId,
      refreshToken: res[0].linkedin_user.refreshToken,
      linkedInAdAccounts: [],
      isOrgDefaultToken: res[0].linkedin_user.isOrgDefaultToken,
    };
  }

  async createLinkedInUser(input: LinkedInUser, tx?: Transaction) {
    const invoker = tx ?? db;
    await invoker.insert(linkedInUserTable).values({
      organizationUserId: input.userId,
      refreshToken: input.refreshToken,
    });

    return {
      userId: input.userId,
      refreshToken: input.refreshToken,
      linkedInAdAccounts: [],
      isOrgDefaultToken: input.isOrgDefaultToken,
    };
  }

  async upsertLinkedInUser(input: LinkedInUser, tx?: Transaction) {
    const invoker = tx ?? db;

    const existingLinkedInUser = await invoker
      .select()
      .from(linkedInUserTable)
      .where(eq(linkedInUserTable.organizationUserId, input.userId));

    if (existingLinkedInUser.length > 0) {
      await invoker
        .update(linkedInUserTable)
        .set({
          refreshToken: input.refreshToken,
          isOrgDefaultToken: input.isOrgDefaultToken,
          updatedAt: new Date(),
        })
        .where(eq(linkedInUserTable.organizationUserId, input.userId));
    } else {
      await invoker.insert(linkedInUserTable).values({
        organizationUserId: input.userId,
        isOrgDefaultToken: input.isOrgDefaultToken,
        refreshToken: input.refreshToken,
      });
    }

    return {
      userId: input.userId,
      refreshToken: input.refreshToken,
      linkedInAdAccounts: [],
      isOrgDefaultToken: input.isOrgDefaultToken,
    };
  }
}
