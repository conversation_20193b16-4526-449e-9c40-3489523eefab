import { ITransaction } from "../../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { LinkedInUser } from "../../../../domain/entites/linkedInUser";

export interface ILinkedInUserRepository {
  getLinkedInUser(
    userId: string,
    tx?: ITransaction,
  ): Promise<LinkedInUser | null>;
  getOneByOrganizationId(
    organizationId: number,
    tx?: ITransaction,
  ): Promise<LinkedInUser | null>;
  createLinkedInUser(
    input: LinkedInUser,
    tx?: ITransaction,
  ): Promise<LinkedInUser>;
  upsertLinkedInUser(
    input: LinkedInUser,
    tx?: ITransaction,
  ): Promise<LinkedInUser>;

  getOneByOrganizationIdDefault(
    organizationId: number,
    tx?: ITransaction,
  ): Promise<LinkedInUser | null>;
}
