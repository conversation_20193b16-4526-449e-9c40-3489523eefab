import { z } from "zod";

import { linkedInAdAccountUrnSchema } from "../../../../domain/valueObjects/linkedInUrns/linkedInAdAccountUrn";

export const getLinkedInUserDefaultResponseDto = z.union([
  z.object({
    userId: z.string().min(1).max(33),
    isOrgDefaultToken: z.boolean(),
    refreshToken: z.string(),
  }),
  z.null(),
]);

export type GetLinkedInUserDefaultResponseDto = z.infer<
  typeof getLinkedInUserDefaultResponseDto
>;
