import {
  getLinkedInApiClientFromOrganizationId,
  getLinkedInFromUserId,
} from "@kalos/linkedin-api";

import { ITransaction } from "../../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IOrganizationRepository } from "../../../../core/application/interfaces/repositories/organization.repository.interface";
import { OrganizationRepository } from "../../../../core/infrastructure/repositories/organization.repository";
import { createUuid } from "../../../../core/utils/uuid";
import { AbTest } from "../../../domain/entites/abTest";
import { AbTestRound } from "../../../domain/entites/abTestRound";
import { AdProgram } from "../../../domain/entites/adProgram";
import { AdSegment } from "../../../domain/entites/adSegment";
import { DeploymentConfig } from "../../../domain/entites/deploymentConfig";
import { Stage } from "../../../domain/entites/stage";
import { AbTestService } from "../../../domain/services/abTest/abTest.service";
import { AbTestRoundService } from "../../../domain/services/abTest/abTestRound.service";
import { AbTestRoundDayService } from "../../../domain/services/abTest/abTestRoundDay.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { AbTestRepository } from "../../../infrastructure/repositories/abTest.repository";
import { AbTestRoundRepository } from "../../../infrastructure/repositories/abTestRound.repository";
import { AbTestRoundDayRepository } from "../../../infrastructure/repositories/abTestRoundDay.repository";
import { AdSegmentBestVariantRepository } from "../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { AdSegmentValuePropRepository } from "../../../infrastructure/repositories/adSegmentValueProp.repository";
import { AdSegmentValuePropCreativeRepository } from "../../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { ConversationSubjectCopyRepository } from "../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdAccountRepository } from "../../../infrastructure/repositories/linkedInAdAccount.repository";
import { LinkedInAdAudienceRepository } from "../../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdProgramCreativeRepository } from "../../../infrastructure/repositories/linkedInAdProgramCreative.repository";
import { LinkedInAdSegmentRepository } from "../../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../../infrastructure/repositories/linkedInCampaign.repository";
import { StageRepository } from "../../../infrastructure/repositories/stage.repository";
import { LinkedInService } from "../../../infrastructure/services/linkedIn.service";
import { IAbTestRepository } from "../../interfaces/infrastructure/repositories/abTest.repository.interface";
import { IAdSegmentBestVariantRepository } from "../../interfaces/infrastructure/repositories/adSegmentBestVariant.repository.interface";
import { IAdSegmentValuePropRepository } from "../../interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IAdSegmentValuePropCreativeRepository } from "../../interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdAccountRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAccount.repository.interface";
import { ILinkedInAdAudienceRepository } from "../../interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { LinkedInAdProgramRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInAdProgram.repository.interface";
import { ILinkedInAdProgramCreativeRepository } from "../../interfaces/infrastructure/repositories/linkedInAdProgramCreative.repository.interface";
import { ILinkedInAdSegmentRepository } from "../../interfaces/infrastructure/repositories/linkedInAdSegment.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import {
  CreateDeploymentInput,
  DeploymentOrchestratorService,
} from "../deploymentOrchestrator.service";
import { AbTestStrategy } from "./abTest.strategy.interface";
import { abTestStrategyRegistry } from "./abTest.strategy.registry";

interface AbTestFacadeServiceContext {
  abTestRepository: IAbTestRepository;
  abTestService: AbTestService;
  stageRepository: IStageRepository;
  campaignRepository: ILinkedInCampaignRepositoryInterface;
  abTestRoundService: AbTestRoundService;
  adSegmentValuePropService: AdSegmentValuePropService;
  abTestRoundDayService: AbTestRoundDayService;
  adProgramCreativeRepository: ILinkedInAdProgramCreativeRepository;
  adSegmentRepository: ILinkedInAdSegmentRepository;
  conversationSubjectRepository: IConversationSubjectCopyRepository;
  linkedInAdAccountRepository: ILinkedInAdAccountRepository;
  organizationRepository: IOrganizationRepository;
  adAudienceRepository: ILinkedInAdAudienceRepository;
  adSegmentValuePropRepository: IAdSegmentValuePropRepository;
  adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  adSegmentBestVariantRepository: IAdSegmentBestVariantRepository;
  linkedInAdProgramRepository: LinkedInAdProgramRepositoryInterface;
}

export class AbTestFacadeService {
  private readonly strategy: AbTestStrategy;
  private constructor(
    private readonly type: AbTest["type"],
    private readonly ctx: AbTestFacadeServiceContext,
  ) {
    this.strategy = new abTestStrategyRegistry[this.type]({
      abTestService: this.ctx.abTestService,
      campaignRepository: this.ctx.campaignRepository,
      stageRepository: this.ctx.stageRepository,
      adSegmentValuePropService: this.ctx.adSegmentValuePropService,
      adProgramCreativeRepository: this.ctx.adProgramCreativeRepository,
      adSegmentRepository: this.ctx.adSegmentRepository,
      conversationSubjectRepository: this.ctx.conversationSubjectRepository,
      adSegmentValuePropCreativeRepository:
        this.ctx.adSegmentValuePropCreativeRepository,
    });
  }

  hasId(set: Set<any>, idToCheck: string): boolean {
    for (const item of set) {
      // works with 2 different data models for current ads and past ads
      const itemId = item.id ?? item.sponsoredCreativeId;
      if (itemId === idToCheck) {
        return true;
      }
    }
    return false;
  }

  async runAbTest(stageId: string, tx?: ITransaction) {
    const stage = await this.ctx.stageRepository.getStage(stageId);
    if (!stage) {
      throw new Error("Stage not found");
    }
    if (stage.status !== "RUNNING") {
      throw new Error("Stage is not running");
    }
    const abTest = await this.ctx.abTestService.createAbTest(
      stageId,
      this.type,
      tx,
    );
    const reasourceIds = await this.strategy.getDataToSetupRounds(
      abTest,
      stage.adSegmentId,
      tx,
    );

    if (reasourceIds.resourceIds.length === 0) {
      throw new Error("No resource ids provided");
    }

    if (reasourceIds.resourceIds.length === 1) {
      const reasource = reasourceIds.resourceIds[0];
      if (!reasource) {
        throw new Error("No resource id provided");
      }
      await this.ctx.abTestService.setAbTestStausToInProgress(
        abTest.stageId,
        stage,
        this.type,
        tx,
      );
      await this.ctx.abTestService.setAbTestStausToCompleted(
        abTest.stageId,
        reasource,
        this.type,
        tx,
      );
      return null;
    }

    await this.ctx.abTestRoundService.createRoundsForAbTest(
      abTest,
      reasourceIds.resourceIds,
      tx,
    );

    await this.ctx.abTestService.setAbTestStausToInProgress(
      abTest.stageId,
      stage,
      this.type,
      tx,
    );

    return abTest;
  }

  async runRound(abTestId: string, tx?: ITransaction) {
    // Get AbTest
    const abTest = await this.ctx.abTestService.getAbTest(
      abTestId,
      this.type,
      tx,
    );
    if (!abTest) {
      throw new Error("AbTest not found");
    }
    if (abTest.status !== "IN_PROGRESS") {
      throw new Error("AbTest is not in progress");
    }

    // Get Rounds
    const rounds = await this.ctx.abTestRoundService.getRoundsForAbTest(
      abTest.stageId,
      this.type,
      tx,
    );

    // Check if there is a running round
    const runningRounds = rounds.filter(
      (round) => round.status === "IN_PROGRESS",
    );
    if (runningRounds.length > 0) {
      throw new Error("There is already a running round");
    }

    // Get a not stated round
    const notStartedRounds = rounds
      .filter((round) => round.status === "NOT_STARTED")
      .sort((a, b) => a.roundIndex - b.roundIndex);
    const roundToRun = notStartedRounds[0];
    if (!roundToRun) {
      throw new Error("No rounds to run");
    }

    await this.ctx.abTestRoundService.setRoundToInProgress(
      roundToRun.id,
      abTest,
      tx,
    );

    return { roundToRunId: roundToRun.id, type: this.type };
  }

  async provisionRoundDay(abTestRoundId: string, tx?: ITransaction) {
    const [abTestRound, adProgramAndAdSegment] = await Promise.all([
      this.ctx.abTestRoundService.getOne(abTestRoundId, this.type, tx),
      this.ctx.abTestRoundService.getAdProgramAndAdSegmentForAbTestRound(
        abTestRoundId,
        this.type,
        tx,
      ),
    ]);

    if (!abTestRound) {
      throw new Error("AbTestRound not found");
    }
    if (abTestRound.status !== "IN_PROGRESS") {
      throw new Error("AbTestRound is not in progress");
    }
    if (!adProgramAndAdSegment) {
      throw new Error("AdProgram or AdSegment not found");
    }

    const { adProgram, adSegment } = adProgramAndAdSegment;
    const deploymentConfigInput =
      await this.strategy.getDeploymentConfigInputData(
        abTestRound,
        adProgram,
        adSegment,
        tx,
      );

    const deploymentOrchestrator =
      await DeploymentOrchestratorService.createFactory(adSegment.id);

    const deployment = await deploymentOrchestrator.createDeployment(
      deploymentConfigInput,
      tx,
    );

    const adTestRoundDay = await this.ctx.abTestRoundDayService.createOne(
      abTestRoundId,
      deployment.id,
      this.type,
      tx,
    );

    return { abTestRoundDayId: adTestRoundDay.id, type: this.type };
  }

  async runRoundDay(abTestRoundDayId: string, tx?: ITransaction) {
    const abTestRoundDay = await this.ctx.abTestRoundDayService.getOne(
      abTestRoundDayId,
      this.type,
      tx,
    );
    if (!abTestRoundDay) {
      throw new Error("AbTestRoundDay not found");
    }
    if (abTestRoundDay.status !== "NOT_STARTED") {
      throw new Error(
        `AbTestRoundDay has already been start. It is currently ${abTestRoundDay.status}`,
      );
    }

    const adProgramAndAdSegment =
      await this.ctx.abTestRoundService.getAdProgramAndAdSegmentForAbTestRound(
        abTestRoundDay.abTestRoundId,
        this.type,
        tx,
      );

    if (!adProgramAndAdSegment) {
      throw new Error("AdProgram or AdSegment not found");
    }

    const { adSegment } = adProgramAndAdSegment;

    const deploymentOrchestrator =
      await DeploymentOrchestratorService.createFactory(adSegment.id);

    await deploymentOrchestrator.startDeployment(
      abTestRoundDay.deploymentConfigId,
      tx,
    );

    const abTestRound = await this.ctx.abTestRoundService.getOne(
      abTestRoundDay.abTestRoundId,
      this.type,
      tx,
    );
    if (!abTestRound) {
      throw new Error("AbTestRound not found");
    }

    await this.ctx.abTestRoundDayService.setRoundDayToInProgress(
      abTestRoundDayId,
      abTestRound,
      this.type,
      tx,
    );
  }

  async endRoundDay(
    abTestRoundDayId: string,
    tx?: ITransaction,
  ): Promise<{
    winner: "CURRENT_BEST" | "CONTENDER" | "NONE";
    adTestRoundId: string;
    type: AbTest["type"];
  }> {
    const abTestRoundDay = await this.ctx.abTestRoundDayService.getOne(
      abTestRoundDayId,
      this.type,
      tx,
    );

    if (!abTestRoundDay) {
      throw new Error("AbTestRoundDay not found");
    }

    if (abTestRoundDay.status !== "IN_PROGRESS") {
      throw new Error("AbTestRoundDay is not in progress");
    }

    const adProgramAndAdSegment =
      await this.ctx.abTestRoundService.getAdProgramAndAdSegmentForAbTestRound(
        abTestRoundDay.abTestRoundId,
        this.type,
        tx,
      );
    if (!adProgramAndAdSegment) {
      throw new Error("AdProgram or AdSegment not found");
    }

    const { adSegment, adProgram } = adProgramAndAdSegment;

    const deploymentOrchestrator =
      await DeploymentOrchestratorService.createFactory(adSegment.id);

    await deploymentOrchestrator.endDeployment(
      abTestRoundDay.deploymentConfigId,
      tx,
    );
    const metrics = await deploymentOrchestrator.getMetrics(
      abTestRoundDay.deploymentConfigId,
      adProgram,
      tx,
    );

    const abTestRound = await this.ctx.abTestRoundService.getOne(
      abTestRoundDay.abTestRoundId,
      this.type,
      tx,
    );
    if (!abTestRound) {
      throw new Error("AbTestRound not found");
    }
    const { currentBestMetrics, contentderMetrics } =
      await this.strategy.processMetrics(metrics, abTestRound);

    let targetMetric: "clicks" | "leads" | "videoViews" | undefined = undefined;
    if (
      adProgram.objectiveType == "BRAND_AWARENESS" ||
      adProgram.objectiveType == "WEBSITE_VISIT"
    ) {
      targetMetric = "clicks";
    } else if (adProgram.objectiveType == "LEAD_GENERATION") {
      targetMetric = "leads";
    } else if (adProgram.objectiveType == "VIDEO_VIEW") {
      targetMetric = "videoViews";
    }
    if (!targetMetric) {
      throw new Error("Invalid ad program objective type");
    }

    const adRoundDaysAll = await this.ctx.abTestRoundDayService.getDaysForRound(
      abTestRound.id,
      this.type,
      tx,
    );
    const adRoundDaysFinished = adRoundDaysAll.filter(
      (day) => day.status === "COMPLETED",
    );

    const currentBestCombinedMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    } = {
      impressions: 0,
      clicks: 0,
      conversions: 0,
      leads: 0,
      cost: 0,
      videoViews: 0,
      sends: 0,
      opens: 0,
    };

    const contenderCombinedMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    } = {
      impressions: 0,
      clicks: 0,
      conversions: 0,
      leads: 0,
      cost: 0,
      videoViews: 0,
      sends: 0,
      opens: 0,
    };

    for (const finishedDay of adRoundDaysFinished) {
      const metricsForFinishedDay = await deploymentOrchestrator.getMetrics(
        finishedDay.deploymentConfigId,
        adProgram,
        tx,
      );
      const finishedDayMetrics = await this.strategy.processMetrics(
        metricsForFinishedDay,
        abTestRound,
      );

      currentBestCombinedMetrics.impressions +=
        finishedDayMetrics.currentBestMetrics.impressions;
      currentBestCombinedMetrics.clicks +=
        finishedDayMetrics.currentBestMetrics.clicks;
      currentBestCombinedMetrics.conversions +=
        finishedDayMetrics.currentBestMetrics.conversions;
      currentBestCombinedMetrics.leads +=
        finishedDayMetrics.currentBestMetrics.leads;
      currentBestCombinedMetrics.cost +=
        finishedDayMetrics.currentBestMetrics.cost;
      currentBestCombinedMetrics.videoViews +=
        finishedDayMetrics.currentBestMetrics.videoViews;
      currentBestCombinedMetrics.sends +=
        finishedDayMetrics.currentBestMetrics.sends;
      currentBestCombinedMetrics.opens +=
        finishedDayMetrics.currentBestMetrics.opens;

      contenderCombinedMetrics.impressions +=
        finishedDayMetrics.contentderMetrics.impressions;
      contenderCombinedMetrics.clicks +=
        finishedDayMetrics.contentderMetrics.clicks;
      contenderCombinedMetrics.conversions +=
        finishedDayMetrics.contentderMetrics.conversions;
      contenderCombinedMetrics.leads +=
        finishedDayMetrics.contentderMetrics.leads;
      contenderCombinedMetrics.cost +=
        finishedDayMetrics.contentderMetrics.cost;
      contenderCombinedMetrics.videoViews +=
        finishedDayMetrics.contentderMetrics.videoViews;
      contenderCombinedMetrics.sends +=
        finishedDayMetrics.contentderMetrics.sends;
      contenderCombinedMetrics.opens +=
        finishedDayMetrics.contentderMetrics.opens;
    }

    currentBestCombinedMetrics.impressions += currentBestMetrics.impressions;
    currentBestCombinedMetrics.clicks += currentBestMetrics.clicks;
    currentBestCombinedMetrics.conversions += currentBestMetrics.conversions;
    currentBestCombinedMetrics.leads += currentBestMetrics.leads;
    currentBestCombinedMetrics.cost += currentBestMetrics.cost;
    currentBestCombinedMetrics.videoViews += currentBestMetrics.videoViews;
    currentBestCombinedMetrics.sends += currentBestMetrics.sends;
    currentBestCombinedMetrics.opens += currentBestMetrics.opens;

    contenderCombinedMetrics.impressions += contentderMetrics.impressions;
    contenderCombinedMetrics.clicks += contentderMetrics.clicks;
    contenderCombinedMetrics.conversions += contentderMetrics.conversions;
    contenderCombinedMetrics.leads += contentderMetrics.leads;
    contenderCombinedMetrics.cost += contentderMetrics.cost;
    contenderCombinedMetrics.videoViews += contentderMetrics.videoViews;
    contenderCombinedMetrics.sends += contentderMetrics.sends;
    contenderCombinedMetrics.opens += contentderMetrics.opens;

    const statsigRes = this.findWinner(
      adProgram.adFormat.type,
      targetMetric,
      abTestRoundDay.dayIndex,
      currentBestCombinedMetrics,
      contenderCombinedMetrics,
    );

    console.log("STATSIG RES", statsigRes);

    await this.ctx.abTestRoundDayService.setRoundDayToCompleted(
      abTestRoundDayId,
      statsigRes.winner == "currentBest" ? "CURRENT_BEST" : "CONTENDER",
      isNaN(statsigRes.currentBestStatsig) ? 0 : statsigRes.currentBestStatsig,
      isNaN(statsigRes.contentderStatsig) ? 0 : statsigRes.contentderStatsig,
      this.type,
      tx,
    );

    if (abTestRoundDay.dayIndex < 5) {
      if (statsigRes.winner == "none") {
        return {
          winner: "NONE",
          adTestRoundId: abTestRoundDay.abTestRoundId,
          type: this.type,
        };
      } else {
        return {
          winner:
            statsigRes.winner == "currentBest" ? "CURRENT_BEST" : "CONTENDER",
          adTestRoundId: abTestRoundDay.abTestRoundId,
          type: this.type,
        };
      }
    } else {
      return {
        winner:
          statsigRes.winner == "currentBest" ? "CURRENT_BEST" : "CONTENDER",
        adTestRoundId: abTestRoundDay.abTestRoundId,
        type: this.type,
      };
    }
  }

  async endRound(
    abTestRoundId: string,
    winner: "CURRENT_BEST" | "CONTENDER",
    tx?: ITransaction,
  ): Promise<
    | {
        status: "CONTINUE";
      }
    | {
        status: "END";
        winnerId: string;
      }
  > {
    const abTestRound = await this.ctx.abTestRoundService.getOne(
      abTestRoundId,
      this.type,
      tx,
    );
    if (!abTestRound) {
      throw new Error("AbTestRound not found");
    }
    if (abTestRound.status !== "IN_PROGRESS") {
      throw new Error("AbTestRound is not in progress");
    }

    await this.ctx.abTestRoundService.setRoundToCompleted(
      abTestRoundId,
      winner,
      this.type,
      tx,
    );

    await this.ctx.abTestRoundService.setCurrentBestForUpcomingRounds(
      abTestRound.abTestId,
      winner == "CURRENT_BEST"
        ? abTestRound.currentBestId
        : abTestRound.contenderId,
      this.type,
      tx,
    );
    const abTestRoundsLeft =
      await this.ctx.abTestRoundService.getRoundsForAbTest(
        abTestRound.abTestId,
        this.type,
        tx,
      );
    const abTestRoundsLeftInProgress = abTestRoundsLeft.filter(
      (round) => round.status === "NOT_STARTED",
    );
    if (abTestRoundsLeftInProgress.length > 0) {
      return {
        status: "CONTINUE",
      };
    } else {
      const winnerId =
        winner == "CURRENT_BEST"
          ? abTestRound.currentBestId
          : abTestRound.contenderId;
      return {
        status: "END",
        winnerId,
      };
    }
  }

  findWinner(
    formatType:
      | "SPONSORED_CONTENT"
      | "SPONSORED_INMAIL"
      | "SPONSORED_CONVERSATION",
    metric: "leads" | "clicks" | "videoViews" | "sends" | "opens",
    dayIndex: number,
    currentBestMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    },
    contentderMetrics: {
      impressions: number;
      clicks: number;
      conversions: number;
      leads: number;
      cost: number;
      videoViews: number;
      sends: number;
      opens: number;
    },
  ): {
    winner: "currentBest" | "contentder" | "none";
    currentBestStatsig: number;
    contentderStatsig: number;
  } {
    const currentBestStatsig = this.calculateStatsig(
      dayIndex,
      formatType == "SPONSORED_INMAIL" && currentBestMetrics[metric] < 1
        ? currentBestMetrics["opens"]
        : currentBestMetrics[metric],
      formatType == "SPONSORED_INMAIL"
        ? currentBestMetrics.sends
        : currentBestMetrics.impressions,
    );
    const contentderStatsig = this.calculateStatsig(
      dayIndex,
      formatType == "SPONSORED_INMAIL" && contentderMetrics[metric] < 1
        ? contentderMetrics["opens"]
        : contentderMetrics[metric],
      formatType == "SPONSORED_INMAIL"
        ? contentderMetrics.sends
        : contentderMetrics.impressions,
    );
    if (
      dayIndex <= 1 &&
      currentBestMetrics.impressions > 1000 &&
      contentderMetrics.impressions > 1000 &&
      currentBestMetrics[metric] > 0 &&
      contentderMetrics[metric] > 0
    ) {
      return {
        winner:
          currentBestStatsig > contentderStatsig ? "currentBest" : "contentder",
        currentBestStatsig,
        contentderStatsig,
      };
    } else if (
      dayIndex == 2 &&
      currentBestMetrics[metric] > 0 &&
      contentderMetrics[metric] > 0
    ) {
      if (currentBestStatsig / contentderStatsig > 1.2) {
        return {
          winner: "currentBest",
          currentBestStatsig,
          contentderStatsig,
        };
      } else if (contentderStatsig / currentBestStatsig > 1.2) {
        return {
          winner: "contentder",
          currentBestStatsig,
          contentderStatsig,
        };
      }
      return {
        winner: "none",
        currentBestStatsig,
        contentderStatsig,
      };
    } else if (dayIndex == 3) {
      if (currentBestStatsig / contentderStatsig > 1.2) {
        return {
          winner: "currentBest",
          currentBestStatsig,
          contentderStatsig,
        };
      } else if (contentderStatsig / currentBestStatsig > 1.2) {
        return {
          winner: "contentder",
          currentBestStatsig,
          contentderStatsig,
        };
      }
      return {
        winner: "none",
        currentBestStatsig,
        contentderStatsig,
      };
    } else if (dayIndex == 4) {
      if (currentBestStatsig / contentderStatsig > 1.1) {
        return {
          winner: "currentBest",
          currentBestStatsig,
          contentderStatsig,
        };
      } else if (contentderStatsig / currentBestStatsig > 1.1) {
        return {
          winner: "contentder",
          currentBestStatsig,
          contentderStatsig,
        };
      }
      return {
        winner: "none",
        currentBestStatsig,
        contentderStatsig,
      };
    } else if (dayIndex >= 5) {
      if (currentBestStatsig > contentderStatsig) {
        return {
          winner: "currentBest",
          currentBestStatsig,
          contentderStatsig,
        };
      } else if (contentderStatsig > currentBestStatsig) {
        return {
          winner: "contentder",
          currentBestStatsig,
          contentderStatsig,
        };
      }
      return {
        winner: "currentBest",
        currentBestStatsig,
        contentderStatsig,
      };
    }
    return {
      winner: "none",
      currentBestStatsig,
      contentderStatsig,
    };
  }

  async endAbTest(abTestId: string, winnerId: string, tx?: ITransaction) {
    const abTest = await this.ctx.abTestService.getAbTest(
      abTestId,
      this.type,
      tx,
    );
    if (!abTest) {
      throw new Error("AbTest not found");
    }
    if (abTest.status !== "IN_PROGRESS") {
      throw new Error("AbTest is not in progress");
    }

    await this.ctx.abTestService.setAbTestStausToCompleted(
      abTestId,
      winnerId,
      this.type,
      tx,
    );
  }

  async addVarientToInProgressAbTest(
    abTestId: string,
    varientId: string,
    tx?: ITransaction,
  ) {
    const abTest = await this.ctx.abTestService.getAbTest(
      abTestId,
      this.type,
      tx,
    );
    if (!abTest) {
      throw new Error("AbTest not found");
    }
    if (abTest.status !== "IN_PROGRESS") {
      throw new Error("AbTest is not in progress");
    }

    const stage = await this.ctx.stageRepository.getStage(abTest.stageId);
    if (!stage) {
      throw new Error("Stage not found");
    }

    await this.ctx.adSegmentBestVariantRepository.upsertOne({
      id: createUuid(),
      adSegmentId: stage.adSegmentId,
      type: this.type,
      variantId: varientId,
    });

    await this.ctx.abTestRoundService.addRoundToInProgressAbTest(
      abTest,
      varientId,
      tx,
    );
  }

  static async addVarientToInProgressAbTestForAdSegment(
    adSegmentId: string,
    varientId: string,
    ctx: {
      stageRepository: IStageRepository;
    },
    tx?: ITransaction,
  ) {
    const stage = await ctx.stageRepository.getCurrentRunningStage(adSegmentId);
    if (!stage) {
      return;
    }
    const abTestType = this.stageTypeToAbTestType(stage.stageType);
    const abTestService = AbTestFacadeService.createFactory(abTestType);
    await abTestService.addVarientToInProgressAbTest(stage.id, varientId, tx);
  }
  static stageTypeToAbTestType(stageType: Stage["stageType"]) {
    switch (stageType) {
      case "audienceTest":
        return "audience";
        break;
      case "valuePropTest":
        return "valueProp";
      case "creativeTest":
        return "creative";
      case "conversationSubjectTest":
        return "conversationSubject";
        break;
      default:
        throw new Error("Invalid stage type");
    }
  }

  calculateStatsig(dayIndex: number, keyResult: number, impressions: number) {
    const keyResultPerImpression = keyResult / impressions;
    const impressionsPerDay = impressions / dayIndex;
    return keyResultPerImpression * impressionsPerDay;
  }

  async getData(
    stageId: string,
    fromDate?: Date,
    toDate?: Date,
    userId?: string,
  ) {
    const [stage, abTest] = await Promise.all([
      this.ctx.stageRepository.getStage(stageId),
      this.ctx.abTestRepository.getByStageId(stageId, this.type),
    ]);

    if (!stage) {
      throw new Error("Stage not found");
    }

    // If we have no ab test, it means the test wasn't started yet
    if (!abTest) {
      return null;
    }

    const adSegment = await this.ctx.adSegmentRepository.getById(
      stage.adSegmentId,
    );

    if (!adSegment) {
      throw new Error("AdSegment not found");
    }

    // Need to get the full AbTest with rounds from getOneWithRoundAndDays
    const abTestWithRounds = await this.ctx.abTestRepository.getOneWithRounds(
      stageId,
      this.type,
    );

    // The return type of getOneWithRoundAndDays has a rounds property
    // so we don't need to check if rounds exists, only if it has any items
    if (!abTestWithRounds.rounds || abTestWithRounds.rounds.length === 0) {
      return {
        stageId: stageId,
        type: this.type,
        status: abTest.status,
        rounds: [],
        ads: [],
      };
    }

    const firstRound = abTestWithRounds.rounds[0];
    if (!firstRound) {
      throw new Error("First round not found");
    }
    const adProgram =
      await this.ctx.abTestRoundService.getAdProgramAndAdSegmentForAbTestRound(
        firstRound.id,
        this.type,
      );
    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const deploymentOrchestrator =
      await DeploymentOrchestratorService.createFactory(adProgram.adSegment.id);

    const sponsoredCreativeIdSet: Set<{
      id: string;
      urn: string;
      adVarients: Record<string, string>;
    }> = new Set();

    const sponsoredCreativeMap = new Map<
      string,
      {
        id: string;
        urn: string;
        adVarients: Record<string, string>;
      }
    >();

    for (const round of abTestWithRounds.rounds) {
      const deploymentConfigInput =
        await this.strategy.getDeploymentConfigInputData(
          {
            id: round.id,
            abTestId: round.abTestId,
            currentBestId: round.currentBestId,
            contenderId: round.contenderId,
            roundIndex: round.roundIndex,
            status: round.status,
            winner: round.winner ?? "CURRENT_BEST",
          },
          adProgram?.adProgram,
          adProgram?.adSegment,
        );

      for (const each of deploymentConfigInput.data.config.campaigns) {
        for (const ad of each.ads) {
          const currDeploymentConfig: CreateDeploymentInput = {
            adType: deploymentConfigInput.adType as any,
            data: {
              adFormat: deploymentConfigInput.data.adFormat as any,
              config: {
                adSegmentId: adProgram.adSegment.id,
                campaigns: [
                  {
                    campaignId: each.campaignId,
                    ads: [ad as any],
                  },
                ],
              },
            },
          };

          const sponsoredCreatives =
            await deploymentOrchestrator.getAds(currDeploymentConfig);
          const sponsoredCreative = sponsoredCreatives[0];
          if (!sponsoredCreative) {
            throw new Error("Sponsored creative not found");
          }

          const campaignId =
            currDeploymentConfig.data.config.campaigns[0]?.campaignId;
          if (!campaignId) {
            throw new Error("Campaign id not found");
          }

          const valuePropId =
            currDeploymentConfig.data.config.campaigns[0]?.ads[0]?.valuePropId;
          if (!valuePropId) {
            throw new Error("Value prop id not found");
          }

          const adAudience =
            await this.ctx.adAudienceRepository.getOne(campaignId);
          const valueProp = await this.ctx.adSegmentValuePropRepository.getOne(
            valuePropId,
            "ACTIVE",
          );
          if (!adAudience || !valueProp) {
            throw new Error("Ad audience or value prop not found");
          }
          const adVarients: Record<string, string> = {};
          adVarients["campaign"] = adAudience.audienceTargetCriteria.include.and
            .map((each) => each.or.map((each) => each.facetUrn))
            .join(",");

          adVarients["valueProp"] = valueProp.valueProp;
          adVarients["campaignId"] = campaignId;

          const thing = currDeploymentConfig.data.config.campaigns[0]?.ads[0];
          if (!thing) {
            throw new Error("Thing not found");
          }

          for (const entry of Object.entries(thing)) {
            const [key, value] = entry;
            if (value !== "valuePropId") {
              adVarients[key] = value;
            }
          }
          adVarients["status"] = round.status;

          // Sponsored Creative Doesn't exist in our map
          if (!sponsoredCreativeMap.has(sponsoredCreative.id)) {
            sponsoredCreativeMap.set(sponsoredCreative.id, {
              id: sponsoredCreative.id,
              adVarients,
              urn: sponsoredCreative.linkedInSponseredCreativeUrn,
            });

            // Sponsored Creative does exist, check to see if new updated status
          } else {
            const existingSponsoredCreative = sponsoredCreativeMap.get(
              sponsoredCreative.id,
            );

            if (!existingSponsoredCreative) {
              continue;
            }

            const sponsoredCreativeStatus =
              existingSponsoredCreative?.adVarients?.status;

            if (round.status === "IN_PROGRESS") {
              existingSponsoredCreative.adVarients.status = round.status;
            }
          }
        }
      }
    }

    const adAccount = await this.ctx.linkedInAdAccountRepository.getOneById(
      adProgram.adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    let linkedInClient;

    if (userId) {
      linkedInClient = await getLinkedInFromUserId(userId);
    } else {
      linkedInClient = await getLinkedInApiClientFromOrganizationId(
        adAccount.organizationId,
      );
    }

    if (!linkedInClient) {
      throw new Error("LinkedIn client not found");
    }

    const linkedInService = new LinkedInService(linkedInClient);

    const metricsForAds = await linkedInService.getAnalyticsForCreatives({
      sponsoredCreativeUrns: Array.from(sponsoredCreativeMap.values()).map(
        (each) => each.urn,
      ),
      startDate: fromDate || adProgram.adProgram.startDatetime,
      endDate: toDate,
      timeGranularity: "ALL",
    });

    const adDataWithMetrics = [];
    for (const each of sponsoredCreativeMap.values()) {
      const metrics = metricsForAds.find(
        (metric) => each.urn === metric.sponsoredCreatieUrn,
      );
      if (!metrics) {
        throw new Error("Metrics not found");
      }
      adDataWithMetrics.push({
        ...each,
        metrics: metrics,
      });
    }

    return {
      ...abTestWithRounds,
      ads: adDataWithMetrics,
    };
  }

  async getAdsForStageId(
    stageId: string,
    sponsoredCreativeUrnMap: Map<string, boolean>,
    fromDate?: Date,
    toDate?: Date,
  ) {
    const [stage, abTest] = await Promise.all([
      this.ctx.stageRepository.getStage(stageId),
      this.ctx.abTestRepository.getByStageId(stageId, this.type),
    ]);

    if (!stage) {
      throw new Error("Stage not found");
    }

    // If we have no ab test, it means the test wasn't started yet
    if (!abTest) {
      return null;
    }

    const adSegment = await this.ctx.adSegmentRepository.getById(
      stage.adSegmentId,
    );

    if (!adSegment) {
      throw new Error("AdSegment not found");
    }

    // Need to get the full AbTest with rounds from getOneWithRoundAndDays
    const abTestWithRounds =
      await this.ctx.abTestRepository.getOneWithRoundAndDays(
        stageId,
        this.type,
      );

    // The return type of getOneWithRoundAndDays has a rounds property
    // so we don't need to check if rounds exists, only if it has any items
    if (!abTestWithRounds.rounds || abTestWithRounds.rounds.length === 0) {
      return {
        stageId: stageId,
        type: this.type,
        status: abTest.status,
        rounds: [],
        ads: [],
      };
    }

    const firstRound = abTestWithRounds.rounds[0];
    if (!firstRound) {
      throw new Error("First round not found");
    }
    const adProgram =
      await this.ctx.abTestRoundService.getAdProgramAndAdSegmentForAbTestRound(
        firstRound.id,
        this.type,
      );
    if (!adProgram) {
      throw new Error("Ad program not found");
    }

    const deploymentOrchestrator =
      await DeploymentOrchestratorService.createFactory(adProgram.adSegment.id);

    const sponsoredCreativeMap = new Map<
      string,
      {
        sponsoredCreativeId: string;
        sponsoredCreativeUrn: string;
        adVarients: Record<string, string>;
      }
    >();

    for (const round of abTestWithRounds.rounds) {
      const deploymentConfigInput =
        await this.strategy.getDeploymentConfigInputData(
          {
            id: round.id,
            abTestId: round.abTestId,
            currentBestId: round.currentBestId,
            contenderId: round.contenderId,
            roundIndex: round.roundIndex,
            status: round.status,
            winner: round.winner ?? "CURRENT_BEST",
          },
          adProgram?.adProgram,
          adProgram?.adSegment,
        );

      for (const each of deploymentConfigInput.data.config.campaigns) {
        for (const ad of each.ads) {
          const currDeploymentConfig: CreateDeploymentInput = {
            adType: deploymentConfigInput.adType as any,
            data: {
              adFormat: deploymentConfigInput.data.adFormat as any,
              config: {
                adSegmentId: adProgram.adSegment.id,
                campaigns: [
                  {
                    campaignId: each.campaignId,
                    ads: [ad as any],
                  },
                ],
              },
            },
          };
          const sponsoredCreatives =
            await deploymentOrchestrator.getAds(currDeploymentConfig);
          const sponsoredCreative = sponsoredCreatives[0];
          if (!sponsoredCreative) {
            throw new Error("Sponsored creative not found");
          }

          const campaignId =
            currDeploymentConfig.data.config.campaigns[0]?.campaignId;
          if (!campaignId) {
            throw new Error("Campaign id not found");
          }

          const valuePropId =
            currDeploymentConfig.data.config.campaigns[0]?.ads[0]?.valuePropId;
          if (!valuePropId) {
            throw new Error("Value prop id not found");
          }

          const adAudience =
            await this.ctx.adAudienceRepository.getOne(campaignId);
          const valueProp = await this.ctx.adSegmentValuePropRepository.getOne(
            valuePropId,
            "ACTIVE",
          );
          if (!adAudience || !valueProp) {
            throw new Error("Ad audience or value prop not found");
          }
          const adVarients: Record<string, string> = {};
          adVarients["campaign"] = adAudience.audienceTargetCriteria.include.and
            .map((each) => each.or.map((each) => each.facetUrn))
            .join(",");

          adVarients["valueProp"] = valueProp.valueProp;
          adVarients["campaignId"] = campaignId;
          const thing = currDeploymentConfig.data.config.campaigns[0]?.ads[0];
          if (!thing) {
            throw new Error("Thing not found");
          }
          for (const entry of Object.entries(thing)) {
            const [key, value] = entry;
            if (value !== "valuePropId") {
              adVarients[key] = value;
            }
          }

          if (sponsoredCreativeUrnMap.has(sponsoredCreative.id)) {
            continue;
          }

          if (!sponsoredCreativeMap.has(sponsoredCreative.id)) {
            sponsoredCreativeMap.set(sponsoredCreative.id, {
              sponsoredCreativeId: sponsoredCreative.id,
              adVarients,
              sponsoredCreativeUrn:
                sponsoredCreative.linkedInSponseredCreativeUrn,
            });
          }
        }
      }
    }

    const sponsoredCreativesArray = Array.from(sponsoredCreativeMap.values());

    return {
      ...abTestWithRounds,
      ads: sponsoredCreativesArray,
    };
  }

  async getMetricDataForCreatives(
    sponsoredCreativeUrns: string[],
    linkedInAdProgramId: string,
    fromDate?: Date,
    toDate?: Date,
    userId?: string,
  ) {
    const adProgram =
      await this.ctx.linkedInAdProgramRepository.getOne(linkedInAdProgramId);

    if (!adProgram) {
      throw new Error("[getMetricDataForCreatives] No ad program found");
    }

    const adAccount = await this.ctx.linkedInAdAccountRepository.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("[getMetricDataForCreatives] Ad account not found");
    }
    let linkedInClient;

    if (userId) {
      linkedInClient = await getLinkedInFromUserId(userId);
    } else {
      linkedInClient = await getLinkedInApiClientFromOrganizationId(
        adAccount.organizationId,
      );
    }
    if (!linkedInClient) {
      throw new Error("[getMetricDataForCreatives] LinkedIn client not found");
    }

    const linkedInService = new LinkedInService(linkedInClient);

    const metricsForAds = await linkedInService.getAnalyticsForCreatives({
      sponsoredCreativeUrns: sponsoredCreativeUrns.map((urn) => urn),
      startDate: fromDate || adProgram.startDatetime,
      endDate: toDate,
      timeGranularity: "ALL",
    });

    const adDataWithMetrics: Record<string, any> = {};

    metricsForAds.forEach((adMetric) => {
      adDataWithMetrics[adMetric.sponsoredCreatieUrn] = { ...adMetric };
    });

    return adDataWithMetrics;
  }

  async getWouldBeRoundsAds(
    stageId: string,
    adProgram: AdProgram,
    adSegment: AdSegment,
    userId?: string,
  ) {
    const stage = await this.ctx.stageRepository.getStage(stageId);
    if (!stage) {
      throw new Error("Stage not found");
    }

    const reasourceIds = await this.strategy.getDataToSetupRounds(
      {
        stageId: "",
        type: this.type,
        status: "NOT_STARTED",
      },
      stage.adSegmentId,
    );

    const deploymentOrchestrator =
      await DeploymentOrchestratorService.createFactory(adSegment.id);

    const sponsoredCreativeIdSet: Set<{
      id: string;
      urn: string;
      adVarients: Record<string, string>;
    }> = new Set();

    for (const round of reasourceIds.resourceIds) {
      const deploymentConfigInput =
        await this.strategy.getDeploymentConfigInputData(
          {
            id: "",
            abTestId: "",
            currentBestId: round,
            contenderId: round,
            roundIndex: 0,
            status: "NOT_STARTED",
          },
          adProgram,
          adSegment,
        );
      for (const each of deploymentConfigInput.data.config.campaigns) {
        for (const ad of each.ads) {
          const currDeploymentConfig: CreateDeploymentInput = {
            adType: deploymentConfigInput.adType as any,
            data: {
              adFormat: deploymentConfigInput.data.adFormat as any,
              config: {
                adSegmentId: adSegment.id,
                campaigns: [
                  {
                    campaignId: each.campaignId,
                    ads: [ad as any],
                  },
                ],
              },
            },
          };
          console.log(
            "DEPLOYMENT CONFIG INPUT",
            JSON.stringify(currDeploymentConfig, null, 2),
          );
          const sponsoredCreatives =
            await deploymentOrchestrator.getAds(currDeploymentConfig);
          const sponsoredCreative = sponsoredCreatives[0];
          if (!sponsoredCreative) {
            throw new Error("Sponsored creative not found");
          }

          const campaignId =
            currDeploymentConfig.data.config.campaigns[0]?.campaignId;
          if (!campaignId) {
            throw new Error("Campaign id not found");
          }

          const valuePropId =
            currDeploymentConfig.data.config.campaigns[0]?.ads[0]?.valuePropId;
          if (!valuePropId) {
            throw new Error("Value prop id not found");
          }

          const adAudience =
            await this.ctx.adAudienceRepository.getOne(campaignId);
          const valueProp = await this.ctx.adSegmentValuePropRepository.getOne(
            valuePropId,
            "ACTIVE",
          );
          if (!adAudience || !valueProp) {
            throw new Error("Ad audience or value prop not found");
          }
          const adVarients: Record<string, string> = {};
          adVarients["campaign"] = adAudience.audienceTargetCriteria.include.and
            .map((each) => each.or.map((each) => each.facetUrn))
            .join(", ");
          adVarients["campaignId"] = campaignId;

          adVarients["valueProp"] = valueProp.valueProp;
          const thing = currDeploymentConfig.data.config.campaigns[0]?.ads[0];
          if (!thing) {
            throw new Error("Value prop id not found");
          }

          for (const entry of Object.entries(thing)) {
            const [key, value] = entry;
            if (value !== "valuePropId") {
              adVarients[key] = value;
            }
          }

          sponsoredCreativeIdSet.add({
            id: sponsoredCreative.id,
            adVarients: adVarients,
            urn: sponsoredCreative.linkedInSponseredCreativeUrn,
          });
        }
      }
    }

    const adAccount = await this.ctx.linkedInAdAccountRepository.getOneById(
      adProgram.linkedInAdAccountId,
    );
    if (!adAccount) {
      throw new Error("Ad account not found");
    }

    let linkedInClient;

    if (userId) {
      linkedInClient = await getLinkedInFromUserId(userId);
    } else {
      linkedInClient = await getLinkedInApiClientFromOrganizationId(
        adAccount.organizationId,
      );
    }
    if (!linkedInClient) {
      throw new Error("LinkedIn client not found");
    }

    const linkedInService = new LinkedInService(linkedInClient);

    const metricsForAds = await linkedInService.getAnalyticsForCreatives({
      sponsoredCreativeUrns: Array.from(sponsoredCreativeIdSet).map(
        (each) => each.urn,
      ),
      startDate: adProgram.startDatetime,
      timeGranularity: "ALL",
    });

    const adDataWithMetrics = [];
    for (const each of Array.from(sponsoredCreativeIdSet)) {
      const metrics = metricsForAds.find(
        (metric) => each.urn === metric.sponsoredCreatieUrn,
      );
      if (!metrics) {
        throw new Error("Metrics not found");
      }
      adDataWithMetrics.push({
        ...each,
        metrics: metrics,
      });
    }

    return {
      ads: adDataWithMetrics,
    };
  }

  static createFactory(type: AbTest["type"]) {
    return new AbTestFacadeService(
      type,
      AbTestFacadeService.constructAbTestFacadeServiceContext(),
    );
  }

  private static constructAbTestFacadeServiceContext(): AbTestFacadeServiceContext {
    return {
      adAudienceRepository: new LinkedInAdAudienceRepository(),
      adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
      linkedInAdAccountRepository: new LinkedInAdAccountRepository(),
      organizationRepository: new OrganizationRepository(),
      abTestRepository: new AbTestRepository() as IAbTestRepository,
      abTestService: new AbTestService({
        abTestRepository: new AbTestRepository() as IAbTestRepository,
      }),
      stageRepository: new StageRepository(),
      campaignRepository: new LinkedInCampaignRepository(),
      abTestRoundService: new AbTestRoundService({
        abTestRoundRepository: new AbTestRoundRepository(),
      }),
      adSegmentValuePropService: new AdSegmentValuePropService(
        new AdSegmentValuePropRepository(),
      ),
      abTestRoundDayService: new AbTestRoundDayService(
        new AbTestRoundDayRepository(),
      ),
      adProgramCreativeRepository: new LinkedInAdProgramCreativeRepository(),
      adSegmentRepository: new LinkedInAdSegmentRepository(),
      conversationSubjectRepository: new ConversationSubjectCopyRepository(),
      adSegmentValuePropCreativeRepository:
        new AdSegmentValuePropCreativeRepository(),
      adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
      linkedInAdProgramRepository: new LinkedInAdProgramRepository(),
    };
  }
}
