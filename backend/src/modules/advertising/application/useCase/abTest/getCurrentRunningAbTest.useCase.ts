import { AbTest } from "../../../domain/entites/abTest";
import { AdCreativeService } from "../../../domain/services/adCreative.service";
import { AdSegmentValuePropService } from "../../../domain/services/adSegmentValueProp.service";
import { ConversationCallToActionCopyService } from "../../../domain/services/conversationCallToActionCopy.service";
import { ConversationMessageCopyService } from "../../../domain/services/conversationMessageCopy.service";
import { ConversationSubjectCopyService } from "../../../domain/services/conversationSubjectCopy.service";
import { LinkedInAdProgramAdCreativeService } from "../../../domain/services/linkedInAdProgramAdCreative.service";
import { SocialPostAdCopyService } from "../../../domain/services/socialPostAdCopy.service";
import { SocialPostCallToActionCopyService } from "../../../domain/services/socialPostCallToActionCopy.service";
import { GetCurrentRunningAbTestDto } from "../../dtos/controllerDtos/abTest/getCurrentRunningAbTest..dto";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestFacadeService } from "../../services/abTest/abTest.facade.service";

export class GetCurrentRunningAbTestUseCase {
  constructor(
    private readonly ctx: {
      stageRepository: IStageRepository;
      adSegmentValuePropService: AdSegmentValuePropService;
      adProgramAdCreativeService: LinkedInAdProgramAdCreativeService;
      adCreativeService: AdCreativeService;
      socialPostCopyService: SocialPostAdCopyService;
      socialPostCallToActionCopyService: SocialPostCallToActionCopyService;
      conversationCopyService: ConversationSubjectCopyService;
      conversationMessageCopyService: ConversationMessageCopyService;
      conversationCallToActionCopyService: ConversationCallToActionCopyService;
    },
  ) {}

  async execute(dto: GetCurrentRunningAbTestDto, userId: string) {
    let stage = await this.ctx.stageRepository.getCurrentRunningStage(
      dto.adSegmentId,
    );

    if (!stage) {
      stage = await this.ctx.stageRepository.getLastRanStage(dto.adSegmentId);

      if (!stage) {
        return null;
      }
    }

    let abTest: AbTest["type"] | undefined = undefined;

    if (stage.stageType === "audienceTest") {
      abTest = "audience";
    } else if (stage.stageType === "valuePropTest") {
      abTest = "valueProp";
    } else if (stage.stageType === "creativeTest") {
      abTest = "creative";
    } else if (stage.stageType === "conversationSubjectTest") {
      abTest = "conversationSubject";
    } else if (stage.stageType === "adCopyTest") {
      abTest = "conversationSubject";
    } else if (stage.stageType === "conversationMessageCopyTest") {
      abTest = "conversationMessageCopy";
    } else if (stage.stageType == "socialPostBodyCopyTest") {
      abTest = "socialPostBodyCopy";
    } else if (stage.stageType === "socialPostCallToActionTest") {
      abTest = "socialPostCallToAction";
    } else if (stage.stageType === "conversationCallToActionTest") {
      abTest = "conversationCallToAction";
    } else {
      throw new Error("Invalid stage type");
    }

    const abTestFacadeService = AbTestFacadeService.createFactory(abTest);
    const res = await abTestFacadeService.getData(
      stage.id,
      dto.fromDate ? new Date(dto.fromDate) : undefined,
      dto.toDate ? new Date(dto.toDate) : undefined,
      userId,
    );

    //TODO-br: Create proper types for adVarients and adcreativeMetadata
    //TODO-BR: make sure stage types are correct!
    if (stage.stageType === "creativeTest" && res?.ads) {
      for (const ad of res.ads) {
        const adCreativeId = ad.adVarients.adCreativeId;

        if (adCreativeId) {
          const adProgramAdCreative =
            await this.ctx.adProgramAdCreativeService.getOne({
              id: adCreativeId,
            });

          if (!adProgramAdCreative) {
            continue;
          }

          const adCreativeData =
            await this.ctx.adCreativeService.getOneWithDownloadPreSignedUrlAndMetadata(
              adProgramAdCreative.adCreativeId,
            );
          console.log("adCreativeID", adCreativeData);
          if (!adCreativeData) {
            continue;
          }
          (ad.adVarients as Record<string, any>).adCreativeMetadata =
            adCreativeData;
        }
      }
    }

    return res;
  }
}
