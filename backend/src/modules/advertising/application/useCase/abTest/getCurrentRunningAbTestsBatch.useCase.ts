import { AbTest } from "../../../domain/entites/abTest";
import { GetCurrentRunningAbTestsBatchDto } from "../../dtos/controllerDtos/abTest/getCurrentRunningAbTests.dto";
import { IStageRepository } from "../../interfaces/infrastructure/repositories/stage.repository.interface";
import { AbTestFacadeService } from "../../services/abTest/abTest.facade.service";

export class GetCurrentRunningAbTestsBatchUseCase {
  constructor(
    private readonly ctx: {
      stageRepository: IStageRepository;
    },
  ) {}

  async execute(dto: GetCurrentRunningAbTestsBatchDto, userId: string) {
    const result: Record<string, any> = {};

    // Process each segment ID in parallel
    await Promise.all(
      dto.adSegmentIds.map(async (adSegmentId) => {
        const stage =
          await this.ctx.stageRepository.getCurrentRunningStage(adSegmentId);

        if (!stage) {
          result[adSegmentId] = null;
          return;
        }

        let abTest: AbTest["type"] | undefined = undefined;

        if (stage.stageType === "audienceTest") {
          abTest = "audience";
        } else if (stage.stageType === "valuePropTest") {
          abTest = "valueProp";
        } else if (stage.stageType === "creativeTest") {
          abTest = "creative";
        } else if (stage.stageType === "conversationSubjectTest") {
          abTest = "conversationSubject";
        } else {
          throw new Error("Invalid stage type");
        }

        const abTestFacadeService = AbTestFacadeService.createFactory(abTest);
        const data = await abTestFacadeService.getData(
          stage.id,
          dto.fromDate ? new Date(dto.fromDate) : undefined,
          dto.toDate ? new Date(dto.toDate) : undefined,
          userId,
        );

        result[adSegmentId] = data;
      }),
    );

    return result;
  }
}
