import { LinkedInUserService } from "../../../domain/services/linkedInUser.service";
import { GetLinkedInUserDefaultResponseDto } from "../../dtos/controllerDtos/linkerInUserDtos/getLinkedInUserDefault.dto";

export async function getDefaultLinkedInUserUseCase(ctx: {
  linkedInUserService: LinkedInUserService;
  organizationId: number;
}): Promise<GetLinkedInUserDefaultResponseDto> {
  const linkedInUser =
    await ctx.linkedInUserService.getOneByOrganizationIdDefault(
      ctx.organizationId,
    );
  if (!linkedInUser) {
    return null;
  }
  return linkedInUser;
}
