import { useState } from "react";
import { api } from "@/trpc/client";
import { DotsVerticalIcon } from "@radix-ui/react-icons";
import {
  Award,
  Calendar,
  Eye,
  LoaderCircleIcon,
  TrendingUp,
  Trophy,
} from "lucide-react";
import { z } from "zod";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@kalos/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { RadioGroup, RadioGroupItem } from "@kalos/ui/radiogroup";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kalos/ui/table/table";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";
import { AbTestRoundDataDto } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/dtos/abTestRoundData.dto";
import { AdProgram } from "../../../../../../../../../backend/src/modules/advertising/domain/entites/adProgram";
import { AbTestRoundsDetailsModal } from "./abTestRoundsDetailsModal";

const metricsKeySchema = z.object({
  impressions: z.number().nullable(),
  clicks: z.number().nullable(),
  conversions: z.number().nullable(),
  leads: z.number().nullable(),
  videoViews: z.number().nullable(),
  sends: z.number().nullable(),
  opens: z.number().nullable(),
  cost: z.number().nullable(),
  actionClicks: z.number().nullable(),
  totalEngagements: z.number().nullable(),
  oneClickLeadFormOpens: z.number().nullable(),
  landingPageClicks: z.number().nullable(),
  videoCompletions: z.number().nullable(),
  videoFirstQuartileCompletions: z.number().nullable(),
  videoMidpointCompletions: z.number().nullable(),
  videoThirdQuartileCompletions: z.number().nullable(),
  videoStarts: z.number().nullable(),
  externalWebsiteConversions: z.number().nullable(),
});

type Metrics = z.infer<typeof metricsKeySchema>;
type MetircsKey = keyof Metrics;

type MetricConfig = {
  currentBestValue: number | null;
  contenderValue: number | null;
  label: string;
}[];

function getMetrics(
  data: Metrics,
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION",
): (number | null)[] {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessMetrics(data);
    } else {
      return singleImageAwarenessMetrics(data);
    }
  }
  return singleImageAwarenessMetrics(data);
}

function getHeaders(
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION",
) {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessHeaders;
    } else {
      return singleImageAwarenessHeaders;
    }
  }
  return [];
}

function getCpm(impressions: number | null, cost: number | null) {
  if (!impressions || !cost) return null;
  return (cost / impressions) * 1000;
}

function getCtr(clicks: number | null, impressions: number | null) {
  if (!clicks || !impressions || impressions === 0) return null;
  return (clicks / impressions) * 100;
}

function getCpc(clicks: number | null, cost: number | null) {
  if (!clicks || !cost) return null;
  return cost / clicks;
}

const singleImageAwarenessHeaders = [
  "Impressions",
  "Clicks",
  "CPM",
  "CTR",
  "CPC",
  "Total Engagements",
];
function singleImageAwarenessMetrics(data: Metrics): (number | null)[] {
  const cpm = getCpm(data.impressions, data.cost);
  const ctr = getCtr(data.clicks, data.impressions);
  const cpc = getCpc(data.clicks, data.cost);
  return [data.impressions, data.clicks, cpm, ctr, cpc, data.totalEngagements];
}

// Component for individual round row that fetches its own data
function AbTestRoundRow({
  adFormat,
  objectiveType,
  roundId,
  roundIndex,
  adSegmentId,
  abTestType,
  status,
  winner,
}: {
  roundId: string;
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION";
  roundIndex: number;
  adSegmentId: string;
  abTestType: AbTestType;
  status: "past" | "current" | "upcoming";
  winner?: "CURRENT_BEST" | "CONTENDER";
}) {
  // Always call hooks at the top level - no conditional hook calls
  const roundDataQuery =
    api.v2.ads.newAbTestController.getAbTestRoundData.useQuery({
      abTestRoundId: roundId,
      adSegmentId: adSegmentId,
      type: abTestType,
    });

  // Helper functions
  const formatNumber = (num: number | null) => {
    if (num === null || num === undefined) return "-";
    return num.toLocaleString();
  };

  const formatCurrency = (num: number | null) => {
    if (num === null || num === undefined) return "-";
    return `$${num.toFixed(2)}`;
  };

  const calculateCTR = (clicks: number | null, impressions: number | null) => {
    if (!clicks || !impressions || impressions === 0) return "-";
    return `${((clicks / impressions) * 100).toFixed(2)}%`;
  };

  const calculateCostPerClick = (
    cost: number | null,
    clicks: number | null,
  ) => {
    if (!cost || !clicks || clicks === 0) return "-";
    return formatCurrency(cost / clicks);
  };

  // Handle different statuses after all hooks have been called
  if (status === "upcoming") {
    // Upcoming rounds show only the new challenger (not the current best since it's already shown)

    return (
      <TableRow className="hover:bg-gray-50">
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            {roundDataQuery.data && (
              <span>{roundDataQuery.data.contender.adName}</span>
            )}
          </div>
        </TableCell>
        {roundDataQuery.data &&
          getMetrics(
            roundDataQuery.data.contender.metrics,
            adFormat,
            objectiveType,
          ).map((metric) => <TableCell>{metric ?? "-"}</TableCell>)}
      </TableRow>
    );
  }

  if (status === "past") {
    // Past rounds show only the losing ad (the one that didn't win)

    return (
      <TableRow className="hover:bg-gray-50">
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            <span>
              {roundDataQuery.data
                ? roundDataQuery.data.winner === "CURRENT_BEST"
                  ? roundDataQuery.data.contender.adName
                  : roundDataQuery.data.currentBest.adName
                : "-"}
            </span>
          </div>
        </TableCell>
        {roundDataQuery.data &&
          getMetrics(
            roundDataQuery.data.winner === "CURRENT_BEST"
              ? roundDataQuery.data.contender.metrics
              : roundDataQuery.data.currentBest.metrics,
            adFormat,
            objectiveType,
          ).map((metric) => <TableCell>{metric ?? "-"}</TableCell>)}
      </TableRow>
    );
  }

  // Current round - show detailed data
  if (roundDataQuery.isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={11} className="py-4 text-center text-gray-500">
          Loading round data...
        </TableCell>
      </TableRow>
    );
  }

  if (roundDataQuery.isError || !roundDataQuery.data) {
    return (
      <TableRow>
        <TableCell colSpan={11} className="py-4 text-center text-red-500">
          Error loading round data
        </TableCell>
      </TableRow>
    );
  }

  const roundData = roundDataQuery.data;

  return (
    <>
      {/* Current Best */}
      <TableRow className="hover:bg-gray-50">
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            <span>{roundData.currentBest.adName}</span>
          </div>
        </TableCell>
        {getMetrics(roundData.currentBest.metrics, adFormat, objectiveType).map(
          (metric) => (
            <TableCell>{metric ?? "-"}</TableCell>
          ),
        )}
      </TableRow>
      {/* Contender */}
      <TableRow className="hover:bg-gray-50">
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            <span>{roundData.contender.adName}</span>
          </div>
        </TableCell>
        {getMetrics(roundData.contender.metrics, adFormat, objectiveType).map(
          (metric) => (
            <TableCell>{metric ?? "-"}</TableCell>
          ),
        )}
      </TableRow>
    </>
  );
}

export function CurrentAbTest({
  abTestId,
  adFormat,
  objectiveType,
  abTestType,
  adSegmentId,
}: {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION";
}) {
  const abTestsQuery = api.v2.ads.newAbTestController.getAbTests.useQuery({
    adSegmentId: adSegmentId,
  });

  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery(
    {
      abTestId: abTestId,
      type: abTestType,
      adSegmentId: adSegmentId,
    },
    {
      // Refetch every 10 seconds if data is null, otherwise every 60 seconds
      refetchInterval: (data) => {
        if (!data) return 10000; // 10 seconds if no data
        return 60000; // 60 seconds if we have data
      },
      // Continue refetching even when the component is not in focus
      refetchIntervalInBackground: true,
    },
  );

  // Debug logging
  console.log("AB Test Query Debug:", {
    abTestId,
    abTestType,
    adSegmentId,
    isLoading: abTestQuery.isLoading,
    isError: abTestQuery.isError,
    error: abTestQuery.error,
    data: abTestQuery.data,
  });

  // Get rounds data from abTestData
  const pastRoundsData = abTestQuery.data?.pastRounds || [];
  const currentRoundData = abTestQuery.data?.currentRound;
  const upcomingRoundsData = abTestQuery.data?.upcomingRounds || [];

  if (abTestQuery.isLoading) {
    return <div>Loading AB test data...</div>;
  }

  if (abTestQuery.isError) {
    return <div>Error loading AB test data</div>;
  }

  if (!abTestQuery.data) {
    if (abTestsQuery.isLoading) {
      return <div>Loading AB tests...</div>;
    } else if (abTestsQuery.isError) {
      return <div>Error loading AB tests</div>;
    } else if (abTestsQuery.data?.length === 0 && abTestQuery.data) {
      return (
        <div className="flex h-full w-full items-center justify-center bg-red-500">
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-lg">
                No AB Test Currently Running
              </CardTitle>
              <CardDescription>No Ab Test currently running. </CardDescription>
            </CardHeader>
          </Card>
        </div>
      );
    } else if (
      abTestsQuery.data?.length !== 0 &&
      !abTestQuery.data &&
      abTestsQuery.data &&
      abTestsQuery.data.filter(
        (each) => each.status == "UPCOMING" || each.status == "CURRENT",
      ).length > 0
    ) {
      return (
        <div className="flex h-full w-full items-center justify-center bg-red-500">
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-lg">
                {`${camelCaseToWords(abTestsQuery.data.filter((each) => each.status == "UPCOMING" || each.status == "CURRENT")[abTestsQuery.data.length - 1]?.type ?? "")} test is provisioning`}
              </CardTitle>
              <CardDescription>Data will be available soon.</CardDescription>
            </CardHeader>
          </Card>
        </div>
      );
    } else {
      return <div>Unknown error</div>;
    }
  }

  function camelCaseToWords(camelCase: string): string {
    // Add a space before each uppercase letter that follows a lowercase letter
    const withSpaces = camelCase.replace(/([a-z])([A-Z])/g, "$1 $2");

    // Capitalize the first letter of the string
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1);
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              Learning Goal: {camelCaseToWords(abTestType)}
            </CardTitle>
            <CardDescription>
              Current AB Test Round{" "}
              {abTestQuery.data.currentRound?.roundIndex ?? 0 + 1}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <AbTestRoundsDetailsModal
              abTestId={abTestId}
              abTestType={abTestType}
              adSegmentId={adSegmentId}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Ad Title</TableHead>
                {getHeaders(adFormat, objectiveType).map((header) => (
                  <TableHead key={header}>{header}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Past Ads Section - Show losing ads from previous rounds */}
              <TableRow className="bg-blue-50">
                <TableCell
                  colSpan={11}
                  className="py-3 font-medium text-blue-900"
                >
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4" />
                    <span>Past Ads</span>
                  </div>
                </TableCell>
              </TableRow>
              {pastRoundsData.length > 0 &&
                pastRoundsData.map((round, index) => (
                  <AbTestRoundRow
                    key={`past-${round.id}-${index}`}
                    roundId={round.id}
                    roundIndex={round.roundIndex}
                    adSegmentId={adSegmentId}
                    abTestType={abTestType}
                    status="past"
                    winner={round.winner}
                    adFormat={adFormat}
                    objectiveType={objectiveType}
                  />
                ))}
              {pastRoundsData.length == 0 && (
                <TableRow>
                  <TableCell
                    colSpan={11}
                    className="py-4 text-center text-gray-500"
                  >
                    No past rounds
                  </TableCell>
                </TableRow>
              )}

              {/* Current Ads Section - Show active matchup */}
              <TableRow className="bg-green-50 ">
                <TableCell
                  colSpan={11}
                  className="py-3 pr-1 font-medium text-green-900"
                >
                  <div className="flex w-full items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4" />
                      <span>Current Ads</span>
                    </div>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button size="icon" variant="ghost">
                          <DotsVerticalIcon />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-36 p-0"
                        align="end"
                        side="bottom"
                        sideOffset={5}
                      >
                        {currentRoundData && (
                          <UserResolveAbTestRoundModal
                            abTestRoundId={currentRoundData.id}
                            abTestType={abTestType}
                            adSegmentId={adSegmentId}
                          />
                        )}
                      </PopoverContent>
                    </Popover>
                  </div>
                </TableCell>
              </TableRow>
              {currentRoundData ? (
                <AbTestRoundRow
                  roundId={currentRoundData.id}
                  roundIndex={currentRoundData.roundIndex}
                  adSegmentId={adSegmentId}
                  abTestType={abTestType}
                  status="current"
                  adFormat={adFormat}
                  objectiveType={objectiveType}
                />
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={11}
                    className="py-4 text-center text-gray-500"
                  >
                    No ads currently running
                  </TableCell>
                </TableRow>
              )}

              {/* Upcoming Ads Section - Show new challengers */}
              <TableRow className="bg-orange-50">
                <TableCell
                  colSpan={11}
                  className="py-3 font-medium text-orange-900"
                >
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Upcoming Ads</span>
                  </div>
                </TableCell>
              </TableRow>
              {upcomingRoundsData.length > 0 &&
                upcomingRoundsData.map((round, index) => (
                  <AbTestRoundRow
                    key={`upcoming-${round.id}-${index}`}
                    roundId={round.id}
                    roundIndex={round.roundIndex}
                    adSegmentId={adSegmentId}
                    abTestType={abTestType}
                    status="upcoming"
                    adFormat={adFormat}
                    objectiveType={objectiveType}
                  />
                ))}
              {upcomingRoundsData.length == 0 && (
                <TableRow>
                  <TableCell
                    colSpan={11}
                    className="py-4 text-center text-gray-500"
                  >
                    No upcoming rounds
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

function UserResolveAbTestRoundModal({
  abTestRoundId,
  abTestType,
  adSegmentId,
}: {
  abTestRoundId: string;
  abTestType: AbTestType;
  adSegmentId: string;
}) {
  const abTestRoundDataQuery =
    api.v2.ads.newAbTestController.getAbTestRoundData.useQuery({
      abTestRoundId: abTestRoundId,
      adSegmentId: adSegmentId,
      type: abTestType,
    });

  const [winner, setWinner] = useState<"CURRENT_BEST" | "CONTENDER">(
    "CURRENT_BEST",
  );

  const apiUtils = api.useUtils();

  const [modalOpen, setModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const resolveAbTestRoundMutation =
    api.v2.ads.newAbTestController.userResolveAbTestRound.useMutation({
      onSuccess: async () => {
        await apiUtils.v2.ads.newAbTestController.getAbTestData.invalidate();
        await apiUtils.v2.ads.newAbTestController.getAbTestRoundData.invalidate();
        setIsSubmitting(false);
        setModalOpen(false);
      },
    });

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          <Trophy className="mr-2 h-4 w-4" />
          <span className="text-xs">Select Winner</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Select Winner
          </DialogTitle>
          <DialogDescription>
            Select the winner of the current round
          </DialogDescription>
        </DialogHeader>
        <RadioGroup
          value={winner}
          onValueChange={setWinner as (value: string) => void}
        >
          <Card className="flex items-center justify-between pr-4">
            <CardHeader>
              <CardTitle>
                {abTestRoundDataQuery.data?.currentBest.adName}
              </CardTitle>
            </CardHeader>
            <RadioGroupItem value={"CURRENT_BEST"}>Current Best</RadioGroupItem>
          </Card>
          <Card className="flex items-center justify-between pr-4">
            <CardHeader>
              <CardTitle>
                {abTestRoundDataQuery.data?.contender.adName}
              </CardTitle>
            </CardHeader>
            <RadioGroupItem value={"CONTENDER"}>Current Best</RadioGroupItem>
          </Card>
        </RadioGroup>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setModalOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={async () => {
              setIsSubmitting(true);
              await resolveAbTestRoundMutation.mutateAsync({
                abTestRoundId: abTestRoundId,
                abTestType: abTestType,
                winner: winner,
              });
              setIsSubmitting(false);
              setModalOpen(false);
            }}
            disabled={isSubmitting}
          >
            {isSubmitting && (
              <LoaderCircleIcon className="mr-2 h-4 w-4 animate-spin" />
            )}
            Submit
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
