import React from "react";
import { api } from "@/trpc/client";
import { Trophy } from "lucide-react";
import { z } from "zod";

import { Badge } from "@kalos/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@kalos/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kalos/ui/table/table";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";
import { AbTestRoundsDetailsModal } from "./abTestRoundsDetailsModal";

const metricsKeySchema = z.object({
  impressions: z.number().nullable(),
  clicks: z.number().nullable(),
  conversions: z.number().nullable(),
  leads: z.number().nullable(),
  videoViews: z.number().nullable(),
  sends: z.number().nullable(),
  opens: z.number().nullable(),
  cost: z.number().nullable(),
  actionClicks: z.number().nullable(),
  totalEngagements: z.number().nullable(),
  oneClickLeadFormOpens: z.number().nullable(),
  landingPageClicks: z.number().nullable(),
  videoCompletions: z.number().nullable(),
  videoFirstQuartileCompletions: z.number().nullable(),
  videoMidpointCompletions: z.number().nullable(),
  videoThirdQuartileCompletions: z.number().nullable(),
  videoStarts: z.number().nullable(),
  externalWebsiteConversions: z.number().nullable(),
});

type Metrics = z.infer<typeof metricsKeySchema>;

function getMetrics(
  data: Metrics,
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION",
): (number | null)[] {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessMetrics(data);
    } else {
      return singleImageAwarenessMetrics(data);
    }
  }
  return singleImageAwarenessMetrics(data);
}

function getHeaders(
  format: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL",
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION",
) {
  if (format === "SINGLE_IMAGE") {
    if (objectiveType === "BRAND_AWARENESS") {
      return singleImageAwarenessHeaders;
    } else {
      return singleImageAwarenessHeaders;
    }
  }
  return [];
}

function getCpm(impressions: number | null, cost: number | null) {
  if (!impressions || !cost) return null;
  return (cost / impressions) * 1000;
}

function getCtr(clicks: number | null, impressions: number | null) {
  if (!clicks || !impressions || impressions === 0) return null;
  return (clicks / impressions) * 100;
}

function getCpc(clicks: number | null, cost: number | null) {
  if (!clicks || !cost) return null;
  return cost / clicks;
}

const singleImageAwarenessHeaders = [
  "Impressions",
  "Clicks",
  "CPM",
  "CTR",
  "CPC",
  "Total Engagements",
];
function singleImageAwarenessMetrics(data: Metrics): (number | null)[] {
  const cpm = getCpm(data.impressions, data.cost);
  const ctr = getCtr(data.clicks, data.impressions);
  const cpc = getCpc(data.clicks, data.cost);
  return [data.impressions, data.clicks, cpm, ctr, cpc, data.totalEngagements];
}

// Component for individual ad row that fetches its own data for past AB tests
function PastAbTestAdRow({
  adFormat,
  objectiveType,
  roundId,
  adSegmentId,
  abTestType,
  adType,
  isOverallWinner,
}: {
  roundId: string;
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION";
  adSegmentId: string;
  abTestType: AbTestType;
  adType: "currentBest" | "contender";
  isOverallWinner?: boolean;
}) {
  // Always call hooks at the top level - no conditional hook calls
  const roundDataQuery =
    api.v2.ads.newAbTestController.getAbTestRoundData.useQuery({
      abTestRoundId: roundId,
      adSegmentId: adSegmentId,
      type: abTestType,
    });

  if (roundDataQuery.isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={11} className="py-4 text-center text-gray-500">
          Loading ad data...
        </TableCell>
      </TableRow>
    );
  }

  if (roundDataQuery.isError || !roundDataQuery.data) {
    return (
      <TableRow>
        <TableCell colSpan={11} className="py-4 text-center text-red-500">
          Error loading ad data
        </TableCell>
      </TableRow>
    );
  }

  const roundData = roundDataQuery.data;
  const adData =
    adType === "currentBest" ? roundData.currentBest : roundData.contender;

  return (
    <TableRow className="hover:bg-gray-50">
      <TableCell className="font-medium">
        <div className="flex items-center space-x-2">
          <span>{adData.adName}</span>
          {isOverallWinner && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Trophy className="mr-1 h-3 w-3" />
              Winner
            </Badge>
          )}
        </div>
      </TableCell>
      {getMetrics(adData.metrics, adFormat, objectiveType).map(
        (metric, index) => (
          <TableCell key={index}>{metric ?? "-"}</TableCell>
        ),
      )}
    </TableRow>
  );
}

export function PastAbTests({
  abTestId,
  adFormat,
  objectiveType,
  abTestType,
  adSegmentId,
}: {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
  adFormat: "SINGLE_IMAGE" | "DOCUMENT" | "VIDEO" | "SPONSORED_INMAIL";
  objectiveType: "BRAND_AWARENESS" | "LEAD_GENERATION";
}) {
  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery({
    abTestId: abTestId,
    type: abTestType,
    adSegmentId: adSegmentId,
  });

  function camelCaseToWords(camelCase: string): string {
    // Add a space before each uppercase letter that follows a lowercase letter
    const withSpaces = camelCase.replace(/([a-z])([A-Z])/g, "$1 $2");

    // Capitalize the first letter of the string
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1);
  }

  // Get rounds data from abTestData
  const pastRoundsData = abTestQuery.data?.pastRounds || [];

  // Find the overall winner of the AB test (winner of the last round)
  const overallWinner = React.useMemo(() => {
    if (pastRoundsData.length === 0) return null;

    // Sort rounds by roundIndex to get the last round
    const sortedRounds = [...pastRoundsData].sort(
      (a, b) => b.roundIndex - a.roundIndex,
    );
    const lastRound = sortedRounds[0];

    if (!lastRound) return null;

    // The overall winner is the winner of the last round
    return {
      adId:
        lastRound.winner === "CURRENT_BEST"
          ? lastRound.currentBestId
          : lastRound.contenderId,
      roundId: lastRound.id,
      adType:
        lastRound.winner === "CURRENT_BEST"
          ? ("currentBest" as const)
          : ("contender" as const),
    };
  }, [pastRoundsData]);

  // Create a unique list of ads from all past rounds
  const uniqueAds = React.useMemo(() => {
    const adsMap = new Map();

    pastRoundsData.forEach((round) => {
      // Add currentBest if not already in map
      const currentBestKey = `${round.currentBestId}`;
      if (!adsMap.has(currentBestKey)) {
        adsMap.set(currentBestKey, {
          roundId: round.id,
          adType: "currentBest" as const,
          key: `${round.id}-currentBest`,
          adId: round.currentBestId,
        });
      }

      // Add contender if not already in map
      const contenderKey = `${round.contenderId}`;
      if (!adsMap.has(contenderKey)) {
        adsMap.set(contenderKey, {
          roundId: round.id,
          adType: "contender" as const,
          key: `${round.id}-contender`,
          adId: round.contenderId,
        });
      }
    });

    return Array.from(adsMap.values());
  }, [pastRoundsData]);

  if (abTestQuery.isLoading) {
    return <div>Loading past AB test data...</div>;
  }

  if (abTestQuery.isError) {
    return <div>Error loading past AB test data</div>;
  }

  if (
    !abTestQuery.data ||
    (pastRoundsData.length === 0 && abTestQuery.data.status == "COMPLETED")
  ) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Past Test: {camelCaseToWords(abTestType)}
          </CardTitle>
          <CardDescription>No past rounds available</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center text-gray-500">
            No completed rounds found for this test.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              Past Test: {camelCaseToWords(abTestType)}
            </CardTitle>
            <CardDescription>
              Completed {pastRoundsData.length} round
              {pastRoundsData.length !== 1 ? "s" : ""}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <AbTestRoundsDetailsModal
              abTestId={abTestId}
              abTestType={abTestType}
              adSegmentId={adSegmentId}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Ad Title</TableHead>
                {getHeaders(adFormat, objectiveType).map((header) => (
                  <TableHead key={header}>{header}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {uniqueAds.map((ad) => (
                <PastAbTestAdRow
                  key={ad.key}
                  roundId={ad.roundId}
                  adSegmentId={adSegmentId}
                  abTestType={abTestType}
                  adFormat={adFormat}
                  objectiveType={objectiveType}
                  adType={ad.adType}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
