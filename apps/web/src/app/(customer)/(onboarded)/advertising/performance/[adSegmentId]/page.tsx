"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/trpc/client";
import { FaceIcon, PaddingIcon } from "@radix-ui/react-icons";
import { format, subDays, subMonths } from "date-fns";
import {
  ArrowLeft,
  CalendarIcon,
  ImageIcon,
  MessageCircleIcon,
  XIcon,
} from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { Button } from "@kalos/ui/button";
import { Calendar } from "@kalos/ui/calender";
import { cn } from "@kalos/ui/index";
import { Popover, PopoverContent, PopoverTrigger } from "@kalos/ui/popover";
import { Skeleton } from "@kalos/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kalos/ui/tabs";

// Import custom components
import ActiveAdsTable from "../_components/ActiveAdsTable";
import PastAdsSection from "../_components/PastAdsSection";
import SegmentHeader from "../_components/SegmentHeader";
import UpcomingLearningGoalsTable from "../_components/UpcomingLearningGoalsTable";
import { AdFormatType, SegmentDetail } from "../_types";
// Import custom hooks and utilities
import {
  useAdSegmentPageData,
  useSegmentPageData,
  useUpcomingTests,
} from "../_utils/hooks";
import { mapMetrics } from "../_utils/mappers";
import { CurrentAbTest } from "./currentAbTest";
import { PastAbTests } from "./pastAbTests";

/**
 * Ad Segment Detail Page Component
 * Shows detailed information about a specific ad segment, including:
 * - Current running AB tests and stages
 * - Performance metrics for ad variants
 * - Past and upcoming tests
 */

function useDebounce<T>(value: T, delay: number): T | undefined {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
}

export default function AdSegmentDetailPage({
  params,
}: {
  params: { adSegmentId: string };
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selected, setSelected] = useState<"Running" | "Completed">("Running");

  const [currentTab, setCurrentTab] = useState("active");
  const [expandedRounds, setExpandedRounds] = useState<Record<string, boolean>>(
    {},
  );

  const midCampaignNotification =
    api.v2.ads.adSegmentMidCampaignNotification.getOne.useQuery({
      adSegmentId: params.adSegmentId,
    });

  // Fetch campaign group budget
  const campaignGroupBudgetQuery =
    api.v2.ads.linkedInCampaignGroup.getCampaignGroupBudget.useQuery(
      {
        adSegmentId: params.adSegmentId,
      },
      {
        enabled: !!params.adSegmentId,
      },
    );

  // New date range state (replaces timePeriod)
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  // Extract segment metadata from URL parameters
  const segmentName = searchParams.get("name") || "Unknown Segment";
  const segmentBudgetFromParams = parseFloat(searchParams.get("budget") || "0"); // Keep as fallback
  const segmentFormat = searchParams.get("format");
  const coreSegmentId = searchParams.get("segmentId") || "";
  const adProgramObjectiveType = searchParams.get("objectiveType");
  const startDate = searchParams.get("startDate") || new Date().toISOString();
  const endDate = searchParams.get("endDate") || "";
  const adProgramId = searchParams.get("adProgramId") || "";
  const totalSpent = searchParams.get("totalSpent") || "0";
  const segmentStatus = (searchParams.get("status") || "ACTIVE") as
    | "ACTIVE"
    | "PAUSED"
    | "COMPLETED";
  const [cleanedSegmentFormat, setCleanedSegmentFormat] =
    useState<AdFormatType>("SINGLE_IMAGE");

  // Use campaign group budget if available, otherwise fall back to URL params
  const actualBudget =
    campaignGroupBudgetQuery.data?.totalBudget ?? segmentBudgetFromParams;

  // Create segment detail from URL parameters
  const segmentDetailFromParams = useMemo<SegmentDetail>(
    () => ({
      adProgramId: adProgramId,
      adSegmentId: params.adSegmentId,
      name: segmentName,
      budget: actualBudget,
      startDate: startDate,
      endDate: endDate,
      status: segmentStatus,
      adProgramType: "EVERGREEN",
      metrics: {
        totalImpressions: 0,
        totalClicks: 0,
        totalEngagements: 0,
        totalLeads: 0,
        totalSpent: parseFloat(totalSpent),
      },
    }),
    [
      params.adSegmentId,
      segmentName,
      actualBudget,
      startDate,
      segmentStatus,
      adProgramId,
    ],
  );

  const debouncedFrom = useDebounce(dateRange.from ?? undefined, 300);
  const debouncedTo = useDebounce(dateRange.to ?? undefined, 300);

  useEffect(() => {
    console.log("🔍 debouncedFrom", debouncedFrom);
    console.log("🔍 debouncedTo", debouncedTo);
  }, [debouncedFrom, debouncedTo]);
  // Get AB test data using custom hook (but not segment details)
  const { currentAbTest, isLoading, isError, error } = useAdSegmentPageData(
    params.adSegmentId,
    adProgramId,
    cleanedSegmentFormat,
    debouncedFrom,
    debouncedTo,
  );

  const getPastRanAbTests = api.v2.ads.abTest.getPastRanAbTests.useQuery(
    {
      adSegmentId: params.adSegmentId,
      fromDate: undefined,
      toDate: undefined,
    },
    {
      enabled: !!params.adSegmentId,
      refetchInterval: 60000, // Refresh data every minute
    },
  );

  // const segmentPageData = useSegmentPageData(params.adSegmentId);

  // Initialize expanded state for rounds when data loads
  useEffect(() => {
    if (currentAbTest?.rounds) {
      setExpandedRounds(
        Object.fromEntries(
          currentAbTest.rounds.map((round) => [
            round.id,
            round.status === "IN_PROGRESS",
          ]),
        ),
      );
    }
  }, [currentAbTest]);

  useEffect(() => {
    console.log("Get Past Ran Ab Tests", getPastRanAbTests.data);
  }, [getPastRanAbTests.data]);
  // Log current data for debugging
  useEffect(() => {
    console.log("Ad Segment ID:", params.adSegmentId);
    console.log("Segment Detail from Params:", segmentDetailFromParams);
    console.log("Current AB Test Data:", currentAbTest);
    console.log("Current AB Test Data:", isLoading);

    // Might not need state for segmentFormat
    setCleanedSegmentFormat(segmentFormat as AdFormatType);
  }, [params.adSegmentId, segmentDetailFromParams, currentAbTest]);

  // Handle error state
  if (isError) {
    return (
      <div className="h-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-red-600">Error</h1>
          <p className="mt-2 text-muted-foreground">
            {error ? String(error) : "Failed to load ad segment details"}
          </p>
          <Button
            className="mt-4"
            variant="secondary"
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Performance
          </Button>
        </div>
      </div>
    );
  }

  const [activeTab, setActiveTab] = useState<string>("current");

  const abTestsQuery = api.v2.ads.newAbTestController.getAbTests.useQuery({
    adSegmentId: params.adSegmentId,
  });

  // Get AB tests by status from the abTestsQuery
  const currentAbTestFromQuery = abTestsQuery.data?.find(
    (abTest) => abTest.status === "CURRENT",
  );
  const pastAbTestsFromQuery =
    abTestsQuery.data?.filter((abTest) => abTest.status === "PAST") || [];
  const upcomingAbTestsFromQuery =
    abTestsQuery.data?.filter((abTest) => abTest.status === "UPCOMING") || [];

  return (
    <div className="flex max-h-screen w-full max-w-[100%] flex-col overflow-x-hidden">
      {/* Main Content */}

      <main className="flex-1 p-4 md:p-6">
        {/* Segment Header */}
        <SegmentHeader
          segmentDetail={segmentDetailFromParams}
          router={router}
        />
        {/* Tabs for Ads */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="current">Current Test</TabsTrigger>
            <TabsTrigger value="past">Past Tests</TabsTrigger>
            <TabsTrigger value="upcoming">Upcoming Tests</TabsTrigger>
          </TabsList>
          <TabsContent value="current">
            {abTestsQuery.isLoading ? (
              <div>Loading current test...</div>
            ) : currentAbTestFromQuery ? (
              <CurrentAbTest
                abTestId={currentAbTestFromQuery.stageId}
                adSegmentId={params.adSegmentId}
                abTestType={currentAbTestFromQuery.type}
                adFormat={
                  cleanedSegmentFormat as
                    | "SINGLE_IMAGE"
                    | "DOCUMENT"
                    | "VIDEO"
                    | "SPONSORED_INMAIL"
                }
                objectiveType={
                  adProgramObjectiveType as
                    | "BRAND_AWARENESS"
                    | "LEAD_GENERATION"
                }
              />
            ) : (
              <div>No Current Test</div>
            )}
          </TabsContent>

          <TabsContent value="past">
            {abTestsQuery.isLoading ? (
              <div>Loading past tests...</div>
            ) : pastAbTestsFromQuery.length > 0 ? (
              <div className="space-y-6">
                {pastAbTestsFromQuery.map((abTest) => (
                  <PastAbTests
                    key={abTest.stageId}
                    abTestId={abTest.stageId}
                    adSegmentId={params.adSegmentId}
                    abTestType={abTest.type}
                    adFormat={
                      cleanedSegmentFormat as
                        | "SINGLE_IMAGE"
                        | "DOCUMENT"
                        | "VIDEO"
                        | "SPONSORED_INMAIL"
                    }
                    objectiveType={
                      adProgramObjectiveType as
                        | "BRAND_AWARENESS"
                        | "LEAD_GENERATION"
                    }
                  />
                ))}
              </div>
            ) : (
              <div className="flex h-64 items-center justify-center">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    No Past Tests
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    No completed AB tests found for this segment.
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="upcoming">
            {abTestsQuery.isLoading ? (
              <div>Loading upcoming tests...</div>
            ) : upcomingAbTestsFromQuery.length > 0 ? (
              <div>
                <h3 className="mb-4 text-lg font-semibold">Upcoming Tests</h3>
                <div className="space-y-4">
                  {upcomingAbTestsFromQuery.map((abTest) => (
                    <div key={abTest.stageId} className="rounded-lg border p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{abTest.type} Test</h4>
                          <p className="text-sm text-muted-foreground">
                            Stage ID: {abTest.stageId}
                          </p>
                        </div>
                        <Badge variant="outline">Upcoming</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div>No Upcoming Tests</div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
