"use client";

// In /app/(customer)/(onboarded)/advertising/layout.tsx
import { useEffect } from "react";
import { redirect, useRouter } from "next/navigation";
import { api } from "@/trpc/client";
import { useAuth } from "@clerk/nextjs";

export default function AdvertisingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { userId } = useAuth();

  if (!userId) {
    return null;
  }

  const oauthQuery = api.v2.ads.linkedInUser.getForUser.useQuery();

  const handleSignIn = () => {
    const LINKEDIN_URL = getURLWithQueryParams(
      "https://www.linkedin.com/oauth/v2/authorization",
      {
        response_type: "code",
        client_id: "86yfe63sb37g1s",
        state: `${userId}`,
        redirect_uri: `${getBackendUrl()}/api/oauth2/linkedin/callback`,
        scope:
          "rw_ads w_organization_social r_organization_social w_member_social r_ads r_ads_reporting r_marketing_leadgen_automation",
      },
    );
    window.location.href = LINKEDIN_URL;
  };

  useEffect(() => {
    if (oauthQuery.data !== undefined) {
      // First call is undefined, not sure why, maybe app needs to init? This fixes issue though
      if (!oauthQuery.data) {
        handleSignIn();
      }
    }
  }, [oauthQuery.data]);

  return <>{children}</>;
}

function getBackendUrl() {
  // "https://url"
  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

  return baseUrl;
}

const getURLWithQueryParams = (base: string, params: Object) => {
  const query = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join("&");

  return `${base}?${query}`;
};
