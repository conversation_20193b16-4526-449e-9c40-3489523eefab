import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { <PERSON><PERSON>rov<PERSON> } from "@clerk/nextjs";

import "./globals.css";

import dynamic from "next/dynamic";
import Link from "next/link";
import { PHProvider } from "@/posthog/posthog";
import { TRPCReactProvider } from "@/trpc/provider";
import { GlobeIcon } from "@radix-ui/react-icons";

import CompanyIcon from "@kalos/ui/icons/comapny-icon";
import WinLossIcon from "@kalos/ui/icons/win-loss-icon";
import { NavBar } from "@kalos/ui/navbar";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Kalos",
  description: "",
};

const PostHogPageView = dynamic(
  () => import("./../posthog/posthog-page-view"),
  {
    ssr: false,
  },
);

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <TRPCReactProvider>
        <html lang="en">
          <body className={inter.className}>{children}</body>
        </html>
      </TRPCReactProvider>
    </ClerkProvider>
  );
}
